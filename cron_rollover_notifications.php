<?php
// Notification script for expiring holiday rollovers
require_once __DIR__ . "/includes/config.php";
require_once __DIR__ . "/includes/functions.php";

function notify_log($message) {
    $log_file = __DIR__ . "/logs/rollover_notifications.log";
    $timestamp = date("Y-m-d H:i:s");
    file_put_contents($log_file, "[$timestamp] $message" . PHP_EOL, FILE_APPEND | LOCK_EX);
}

notify_log("Starting rollover expiry notifications...");

// Find rollovers expiring in 30 days
$sql = "SELECT hr.*, e.name as employee_name, e.email 
        FROM holiday_rollovers hr 
        JOIN employees e ON hr.employee_id = e.id 
        WHERE hr.is_used = 0 
        AND hr.expiry_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
        ORDER BY hr.expiry_date ASC";

$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $employee_name = $row["employee_name"];
        $expiry_date = $row["expiry_date"];
        $days_left = (strtotime($expiry_date) - strtotime(date("Y-m-d"))) / (60 * 60 * 24);
        
        notify_log("Employee $employee_name has rollover expiring on $expiry_date ($days_left days left)");
        
        // Here you can add email notification logic
        // send_email($row["email"], "تنبيه: انتهاء صلاحية الإجازة المرحلة", $message);
    }
} else {
    notify_log("No expiring rollovers found");
}

notify_log("Rollover expiry notifications completed");
$conn->close();
?>