<?php
/**
 * Allowance Types Management - Simplified Version
 */

// Start session and include required files
session_start();
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    redirect('../login.php');
}

// Check if user has permission
if (!is_admin() && !is_supervisor()) {
    set_flash_message('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger');
    redirect('../index.php');
}

// Initialize variables
$allowance_type = [
    'id' => 0,
    'name' => ''
];
$edit_mode = false;
$errors = [];

// Handle edit request
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $edit_id = (int)$_GET['edit'];
    $sql = "SELECT id, name FROM allowance_types WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $edit_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $allowance_type = [
            'id' => $row['id'],
            'name' => $row['name']
        ];
        $edit_mode = true;
    }
}

// Handle delete request
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $delete_id = (int)$_GET['delete'];
    
    // Check if allowance type is being used
    $check_sql = "SELECT COUNT(*) as count FROM employee_allowances WHERE allowance_type_id = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param("i", $delete_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    $check_row = $check_result->fetch_assoc();
    
    if ($check_row['count'] > 0) {
        set_flash_message('لا يمكن حذف هذا النوع لأنه مستخدم في مكافآت الموظفين', 'danger');
    } else {
        $delete_sql = "DELETE FROM allowance_types WHERE id = ?";
        $delete_stmt = $conn->prepare($delete_sql);
        $delete_stmt->bind_param("i", $delete_id);
        
        if ($delete_stmt->execute()) {
            set_flash_message('تم حذف نوع المكافأة بنجاح', 'success');
        } else {
            set_flash_message('حدث خطأ أثناء حذف نوع المكافأة', 'danger');
        }
    }
    
    redirect('types_new.php');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get and sanitize input
    $allowance_type['id'] = isset($_POST['id']) ? (int)$_POST['id'] : 0;
    $allowance_type['name'] = isset($_POST['name']) ? trim($_POST['name']) : '';
    
    // Validate input
    if (empty($allowance_type['name'])) {
        $errors['name'] = 'يرجى إدخال اسم نوع المكافأة.';
    }
    
    // If no errors, save allowance type
    if (empty($errors)) {
        if ($allowance_type['id'] > 0) {
            // Update existing allowance type
            $sql = "UPDATE allowance_types SET name = ? WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("si", $allowance_type['name'], $allowance_type['id']);
            
            if ($stmt->execute()) {
                set_flash_message('تم تحديث نوع المكافأة بنجاح', 'success');
                redirect('types_new.php');
            } else {
                $errors['db'] = 'حدث خطأ أثناء تحديث نوع المكافأة.';
            }
        } else {
            // Insert new allowance type
            $sql = "INSERT INTO allowance_types (name) VALUES (?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("s", $allowance_type['name']);
            
            if ($stmt->execute()) {
                set_flash_message('تم إضافة نوع المكافأة بنجاح', 'success');
                redirect('types_new.php');
            } else {
                $errors['db'] = 'حدث خطأ أثناء إضافة نوع المكافأة.';
            }
        }
    }
}

// Get all allowance types
$sql = "SELECT id, name FROM allowance_types ORDER BY name";
$result = $conn->query($sql);
$allowance_types = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $allowance_types[] = $row;
    }
}

// Include header
require_once '../includes/header.php';
?>

<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">إدارة أنواع المكافآت (مبسط)</h1>
        <a href="../index.php" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded">
            العودة إلى الصفحة الرئيسية
        </a>
    </div>

    <?php if (!empty($errors)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <ul>
                <?php foreach ($errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Form -->
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">
                <?php echo $edit_mode ? 'تعديل نوع المكافأة' : 'إضافة نوع مكافأة جديد'; ?>
            </h2>
            
            <form method="POST">
                <input type="hidden" name="id" value="<?php echo $allowance_type['id']; ?>">
                
                <div class="mb-4">
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        اسم نوع المكافأة <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           value="<?php echo htmlspecialchars($allowance_type['name']); ?>" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" 
                           required>
                </div>
                
                <div class="flex justify-end space-x-2 space-x-reverse">
                    <?php if ($edit_mode): ?>
                        <a href="types_new.php" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded">
                            إلغاء
                        </a>
                    <?php endif; ?>
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded">
                        <?php echo $edit_mode ? 'تحديث' : 'إضافة'; ?>
                    </button>
                </div>
            </form>
        </div>

        <!-- List -->
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">قائمة أنواع المكافآت</h2>
            
            <?php if (empty($allowance_types)): ?>
                <p class="text-gray-500 text-center py-4">لا توجد أنواع مكافآت</p>
            <?php else: ?>
                <div class="space-y-2">
                    <?php foreach ($allowance_types as $type): ?>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
                            <span class="font-medium"><?php echo htmlspecialchars($type['name']); ?></span>
                            <div class="space-x-2 space-x-reverse">
                                <a href="types_new.php?edit=<?php echo $type['id']; ?>" 
                                   class="text-blue-600 hover:text-blue-800">
                                    تعديل
                                </a>
                                <a href="types_new.php?delete=<?php echo $type['id']; ?>" 
                                   class="text-red-600 hover:text-red-800"
                                   onclick="return confirm('هل أنت متأكد من حذف هذا النوع؟')">
                                    حذف
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
