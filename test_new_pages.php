<?php
/**
 * Test New Pages - Simple and Clean
 */

// Include database connection
require_once 'includes/config.php';

echo "<!DOCTYPE html>\n";
echo "<html lang='ar' dir='rtl'>\n";
echo "<head>\n";
echo "<meta charset='UTF-8'>\n";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>\n";
echo "<title>اختبار الصفحات الجديدة</title>\n";
echo "<style>\n";
echo "body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }\n";
echo ".container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n";
echo ".btn { display: inline-block; padding: 15px 30px; margin: 10px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; font-size: 18px; }\n";
echo ".btn:hover { background: #0056b3; }\n";
echo ".btn-success { background: #28a745; }\n";
echo ".btn-success:hover { background: #1e7e34; }\n";
echo ".status { padding: 15px; margin: 10px 0; border-radius: 5px; }\n";
echo ".success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }\n";
echo ".error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }\n";
echo "</style>\n";
echo "</head>\n";
echo "<body>\n";

echo "<div class='container'>\n";
echo "<h1>🧪 اختبار الصفحات الجديدة المبسطة</h1>\n";

// Test database connection
echo "<h2>🔗 اختبار الاتصال بقاعدة البيانات:</h2>\n";
if ($conn->ping()) {
    echo "<div class='status success'>✅ الاتصال بقاعدة البيانات يعمل بشكل صحيح</div>\n";
} else {
    echo "<div class='status error'>❌ مشكلة في الاتصال بقاعدة البيانات</div>\n";
}

// Test table structure
echo "<h2>📊 اختبار بنية الجداول:</h2>\n";

$tables = ['allowance_types', 'deduction_types'];
foreach ($tables as $table) {
    echo "<h3>جدول: $table</h3>\n";
    
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "<div class='status success'>✅ الجدول موجود</div>\n";
        
        // Show columns
        $columns_result = $conn->query("SHOW COLUMNS FROM $table");
        echo "<p><strong>الأعمدة:</strong> ";
        $columns = [];
        while ($row = $columns_result->fetch_assoc()) {
            $columns[] = $row['Field'];
        }
        echo implode(', ', $columns) . "</p>\n";
        
    } else {
        echo "<div class='status error'>❌ الجدول غير موجود</div>\n";
    }
}

// Test simple insert
echo "<h2>🧪 اختبار الإدراج البسيط:</h2>\n";

// Test allowance_types
echo "<h3>اختبار allowance_types:</h3>\n";
try {
    $test_name = "اختبار مكافأة " . date('H:i:s');
    $sql = "INSERT INTO allowance_types (name) VALUES (?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $test_name);
    
    if ($stmt->execute()) {
        $new_id = $conn->insert_id;
        echo "<div class='status success'>✅ تم إدراج '$test_name' بنجاح (ID: $new_id)</div>\n";
        
        // Delete test record
        $conn->query("DELETE FROM allowance_types WHERE id = $new_id");
        echo "<p>تم حذف البيانات التجريبية</p>\n";
    } else {
        echo "<div class='status error'>❌ فشل الإدراج: " . $stmt->error . "</div>\n";
    }
} catch (Exception $e) {
    echo "<div class='status error'>❌ خطأ: " . $e->getMessage() . "</div>\n";
}

// Test deduction_types
echo "<h3>اختبار deduction_types:</h3>\n";
try {
    $test_name = "اختبار استقطاع " . date('H:i:s');
    $sql = "INSERT INTO deduction_types (name) VALUES (?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $test_name);
    
    if ($stmt->execute()) {
        $new_id = $conn->insert_id;
        echo "<div class='status success'>✅ تم إدراج '$test_name' بنجاح (ID: $new_id)</div>\n";
        
        // Delete test record
        $conn->query("DELETE FROM deduction_types WHERE id = $new_id");
        echo "<p>تم حذف البيانات التجريبية</p>\n";
    } else {
        echo "<div class='status error'>❌ فشل الإدراج: " . $stmt->error . "</div>\n";
    }
} catch (Exception $e) {
    echo "<div class='status error'>❌ خطأ: " . $e->getMessage() . "</div>\n";
}

echo "<h2>🚀 الصفحات الجديدة المبسطة:</h2>\n";
echo "<div style='text-align: center; margin: 30px 0;'>\n";
echo "<a href='allowances/types_new.php' class='btn btn-success' target='_blank'>📋 صفحة إدارة أنواع المكافآت الجديدة</a><br>\n";
echo "<a href='deductions/types_new.php' class='btn btn-success' target='_blank'>📋 صفحة إدارة أنواع الاستقطاعات الجديدة</a><br>\n";
echo "</div>\n";

echo "<h2>📝 ملاحظات:</h2>\n";
echo "<div style='background: #e9ecef; padding: 20px; border-radius: 5px;'>\n";
echo "<ul>\n";
echo "<li><strong>الصفحات الجديدة مبسطة تماماً:</strong> تحتوي على حقل الاسم فقط</li>\n";
echo "<li><strong>لا توجد أعمدة إضافية:</strong> تم إزالة جميع الحقول المعقدة</li>\n";
echo "<li><strong>استعلامات بسيطة:</strong> INSERT INTO table (name) VALUES (?)</li>\n";
echo "<li><strong>واجهة نظيفة:</strong> تصميم بسيط وسهل الاستخدام</li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "<div style='text-align: center; margin-top: 30px;'>\n";
echo "<a href='index.php' class='btn'>🏠 العودة إلى الصفحة الرئيسية</a>\n";
echo "</div>\n";

echo "</div>\n";
echo "</body>\n";
echo "</html>\n";

$conn->close();
?>
