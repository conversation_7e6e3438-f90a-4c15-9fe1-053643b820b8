/**
 * Dashboard Charts and Statistics
 *
 * This file handles the dashboard charts and statistics using Chart.js
 */

// Base URL for API requests
const baseUrl = '/newpayroll/';

// Initialize charts when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Load attendance statistics
    loadAttendanceStats();

    // Load salary statistics
    loadSalaryStats();

    // Load department statistics
    loadDepartmentStats();

    // Load recent activities
    loadRecentActivities();
});

/**
 * Load attendance statistics
 */
function loadAttendanceStats() {
    fetch(baseUrl + 'api/dashboard_data.php?type=attendance_stats')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('Error loading attendance stats:', data.error);
                return;
            }

            renderAttendanceChart(data);
        })
        .catch(error => {
            console.error('Error fetching attendance stats:', error);
        });
}

/**
 * Render attendance chart
 *
 * @param {Object} data Attendance statistics data
 */
function renderAttendanceChart(data) {
    const ctx = document.getElementById('attendanceChart').getContext('2d');

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.labels,
            datasets: [
                {
                    label: 'حاضر',
                    data: data.present,
                    backgroundColor: 'rgba(52, 211, 153, 0.2)',
                    borderColor: 'rgba(52, 211, 153, 1)',
                    borderWidth: 2,
                    tension: 0.3
                },
                {
                    label: 'غائب',
                    data: data.absent,
                    backgroundColor: 'rgba(239, 68, 68, 0.2)',
                    borderColor: 'rgba(239, 68, 68, 1)',
                    borderWidth: 2,
                    tension: 0.3
                },
                {
                    label: 'إجازة',
                    data: data.leave,
                    backgroundColor: 'rgba(59, 130, 246, 0.2)',
                    borderColor: 'rgba(59, 130, 246, 1)',
                    borderWidth: 2,
                    tension: 0.3
                },
                {
                    label: 'متأخر',
                    data: data.late,
                    backgroundColor: 'rgba(245, 158, 11, 0.2)',
                    borderColor: 'rgba(245, 158, 11, 1)',
                    borderWidth: 2,
                    tension: 0.3
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    rtl: true,
                    labels: {
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    }
                },
                title: {
                    display: true,
                    text: 'إحصائيات الحضور للشهر الحالي',
                    font: {
                        family: 'Cairo, sans-serif',
                        size: 16
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0,
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    }
                },
                x: {
                    ticks: {
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    }
                }
            }
        }
    });
}

/**
 * Load salary statistics
 */
function loadSalaryStats() {
    fetch(baseUrl + 'api/dashboard_data.php?type=salary_stats')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('Error loading salary stats:', data.error);
                return;
            }

            renderSalaryChart(data);
        })
        .catch(error => {
            console.error('Error fetching salary stats:', error);
        });
}

/**
 * Render salary chart
 *
 * @param {Object} data Salary statistics data
 */
function renderSalaryChart(data) {
    const ctx = document.getElementById('salaryChart').getContext('2d');

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: data.labels,
            datasets: [
                {
                    label: 'إجمالي الرواتب',
                    data: data.total_salaries,
                    backgroundColor: 'rgba(139, 92, 246, 0.6)',
                    borderColor: 'rgba(139, 92, 246, 1)',
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    rtl: true,
                    labels: {
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    }
                },
                title: {
                    display: true,
                    text: 'إجمالي الرواتب للأشهر الستة الماضية',
                    font: {
                        family: 'Cairo, sans-serif',
                        size: 16
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    }
                },
                x: {
                    ticks: {
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    }
                }
            }
        }
    });
}

/**
 * Load department statistics
 */
function loadDepartmentStats() {
    fetch(baseUrl + 'api/dashboard_data.php?type=department_stats')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('Error loading department stats:', data.error);
                return;
            }

            renderDepartmentChart(data);
        })
        .catch(error => {
            console.error('Error fetching department stats:', error);
        });
}

/**
 * Render department chart
 *
 * @param {Object} data Department statistics data
 */
function renderDepartmentChart(data) {
    const ctx = document.getElementById('departmentChart').getContext('2d');

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: data.labels,
            datasets: [
                {
                    data: data.counts,
                    backgroundColor: [
                        'rgba(52, 211, 153, 0.8)',
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(245, 158, 11, 0.8)',
                        'rgba(139, 92, 246, 0.8)',
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(16, 185, 129, 0.8)',
                        'rgba(6, 182, 212, 0.8)',
                        'rgba(251, 113, 133, 0.8)'
                    ],
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                    rtl: true,
                    labels: {
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    }
                },
                title: {
                    display: true,
                    text: 'توزيع الموظفين حسب القسم',
                    font: {
                        family: 'Cairo, sans-serif',
                        size: 16
                    }
                }
            }
        }
    });
}

/**
 * Load recent activities
 */
function loadRecentActivities() {
    fetch(baseUrl + 'api/dashboard_data.php?type=recent_activities')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error('Error loading recent activities:', data.error);
                return;
            }

            updateRecentActivities(data);
        })
        .catch(error => {
            console.error('Error fetching recent activities:', error);
        });
}

/**
 * Update recent activities in the DOM
 *
 * @param {Array} activities Recent activities data
 */
function updateRecentActivities(activities) {
    const container = document.getElementById('recentActivitiesList');

    if (!container) {
        console.error('Recent activities container not found');
        return;
    }

    // Clear existing content
    container.innerHTML = '';

    if (activities.length === 0) {
        container.innerHTML = '<p class="text-gray-500 text-center py-4">لا توجد نشاطات حديثة</p>';
        return;
    }

    // Add activities to the list
    activities.forEach(activity => {
        const time = new Date(activity.timestamp).toLocaleString('ar-SA');

        const item = document.createElement('li');
        item.className = 'py-3';
        item.innerHTML = `
            <div class="flex items-start">
                <div class="bg-gray-100 p-2 rounded-full ml-3">
                    <i class="fas fa-user-circle text-gray-500"></i>
                </div>
                <div>
                    <p class="font-medium">${activity.user}</p>
                    <p class="text-sm text-gray-600">${activity.description}</p>
                    <p class="text-xs text-gray-400 mt-1">${time}</p>
                </div>
            </div>
        `;

        container.appendChild(item);
    });
}
