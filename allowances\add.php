<?php
/**
 * Add Employee Allowance Page
 *
 * This file handles adding a new employee allowance
 */

// Include header
require_once '../includes/header.php';

// Check if user has permission
if (!is_admin() && !is_supervisor()) {
    set_flash_message('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger');
    redirect('../index.php');
}

// Initialize variables
$allowance = [
    'employee_id' => isset($_GET['employee_id']) ? (int)$_GET['employee_id'] : 0,
    'allowance_type_id' => 0,
    'amount' => 0,
    'is_percentage' => 0,
    'effective_date' => date('Y-m-d'),
    'end_date' => date('Y-m-d') // Set default end date to same as effective date
];
$errors = [];

// Get allowance types
$allowance_types_sql = "SELECT * FROM allowance_types ORDER BY name";
$allowance_types_result = $conn->query($allowance_types_sql);
$allowance_types = [];
if ($allowance_types_result) {
    while ($row = $allowance_types_result->fetch_assoc()) {
        $allowance_types[] = $row;
    }
}

// Get employees
$employees_sql = "SELECT id, name, employee_number FROM employees WHERE status = 'active' ORDER BY name";
$employees_result = $conn->query($employees_sql);
$employees = [];
if ($employees_result) {
    while ($row = $employees_result->fetch_assoc()) {
        $employees[] = $row;
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
        $errors['csrf'] = 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.';
    } else {
        // Get and sanitize input
        $allowance['employee_id'] = isset($_POST['employee_id']) ? (int)$_POST['employee_id'] : 0;
        $allowance['allowance_type_id'] = isset($_POST['allowance_type_id']) ? (int)$_POST['allowance_type_id'] : 0;
        $allowance['amount'] = isset($_POST['amount']) ? (float)$_POST['amount'] : 0;
        $allowance['is_percentage'] = isset($_POST['is_percentage']) ? 1 : 0;
        $allowance['effective_date'] = isset($_POST['effective_date']) ? sanitize($_POST['effective_date']) : '';
        $allowance['end_date'] = isset($_POST['end_date']) && !empty($_POST['end_date']) ? sanitize($_POST['end_date']) : null;

        // Validate input
        if ($allowance['employee_id'] <= 0) {
            $errors['employee_id'] = 'يرجى اختيار الموظف.';
        }

        if ($allowance['allowance_type_id'] <= 0) {
            $errors['allowance_type_id'] = 'يرجى اختيار نوع المكافأة.';
        }

        if ($allowance['amount'] <= 0) {
            $errors['amount'] = 'يرجى إدخال مبلغ المكافأة.';
        }

        if (empty($allowance['effective_date'])) {
            $errors['effective_date'] = 'يرجى تحديد تاريخ بداية المكافأة.';
        }

        if (!empty($allowance['end_date']) && strtotime($allowance['effective_date']) > strtotime($allowance['end_date'])) {
            $errors['date_range'] = 'تاريخ بداية المكافأة يجب أن يكون قبل تاريخ نهايتها.';
        }

        // Check if allowance already exists for this employee and type
        if ($allowance['employee_id'] > 0 && $allowance['allowance_type_id'] > 0) {
            $sql = "SELECT COUNT(*) as count FROM employee_allowances
                    WHERE employee_id = ?
                    AND allowance_type_id = ?
                    AND ((? BETWEEN effective_date AND IFNULL(end_date, '9999-12-31'))
                    OR (? BETWEEN effective_date AND IFNULL(end_date, '9999-12-31'))
                    OR (effective_date >= ? AND (end_date <= ? OR end_date IS NULL)))";
            $stmt = $conn->prepare($sql);
            // Prepare end date value
            $end_date = !empty($allowance['end_date']) ? $allowance['end_date'] : '9999-12-31';

            $stmt->bind_param("iiisss",
                $allowance['employee_id'],
                $allowance['allowance_type_id'],
                $allowance['effective_date'],
                $end_date,
                $allowance['effective_date'],
                $end_date
            );
            $stmt->execute();
            $result = $stmt->get_result();
            $row = $result->fetch_assoc();

            if ($row['count'] > 0) {
                $errors['duplicate'] = 'توجد مكافأة من نفس النوع لهذا الموظف في نفس الفترة.';
            }
        }

        // If no errors, insert allowance
        if (empty($errors)) {
            $sql = "INSERT INTO employee_allowances (employee_id, allowance_type_id, amount, is_percentage, effective_date, end_date, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, NOW())";

            $stmt = $conn->prepare($sql);
            // Prepare end date value for insert
            $end_date = !empty($allowance['end_date']) ? $allowance['end_date'] : null;

            $stmt->bind_param("iidiss",
                $allowance['employee_id'],
                $allowance['allowance_type_id'],
                $allowance['amount'],
                $allowance['is_percentage'],
                $allowance['effective_date'],
                $end_date
            );

            if ($stmt->execute()) {
                // Get employee name
                $employee_name = '';
                foreach ($employees as $employee) {
                    if ($employee['id'] == $allowance['employee_id']) {
                        $employee_name = $employee['name'];
                        break;
                    }
                }

                // Log activity
                log_activity('add_employee_allowance', 'تم إضافة مكافأة جديدة للموظف: ' . $employee_name);

                // Set success message
                set_flash_message('تم إضافة المكافأة بنجاح', 'success');

                // Redirect to allowances list
                redirect("index.php?employee_id=" . $allowance['employee_id']);
            } else {
                $errors['db'] = 'حدث خطأ أثناء إضافة المكافأة. يرجى المحاولة مرة أخرى.';
            }
        }
    }
}

// Generate CSRF token
$csrf_token = generate_csrf_token();
?>

<div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold">إضافة مكافأة جديدة</h1>
    <a href="index.php<?php echo $allowance['employee_id'] ? "?employee_id=" . $allowance['employee_id'] : ''; ?>" class="bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-md flex items-center">
        <i class="fas fa-arrow-right ml-2"></i> العودة إلى قائمة المكافآت
    </a>
</div>

<?php if (!empty($errors)): ?>
    <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 mb-6 rounded-md">
        <ul class="list-disc list-inside">
            <?php foreach ($errors as $error): ?>
                <li><?php echo $error; ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<div class="bg-white rounded-lg shadow-md p-6">
    <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <!-- Employee -->
            <div>
                <label for="employee_id" class="block text-sm font-medium text-gray-700 mb-1">الموظف <span class="text-red-600">*</span></label>
                <select id="employee_id" name="employee_id" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" required>
                    <option value="">اختر الموظف</option>
                    <?php foreach ($employees as $employee): ?>
                        <option value="<?php echo $employee['id']; ?>" <?php echo $allowance['employee_id'] == $employee['id'] ? 'selected' : ''; ?>>
                            <?php echo $employee['name']; ?> (<?php echo $employee['employee_number']; ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <!-- Allowance Type -->
            <div>
                <label for="allowance_type_id" class="block text-sm font-medium text-gray-700 mb-1">نوع المكافأة <span class="text-red-600">*</span></label>
                <select id="allowance_type_id" name="allowance_type_id" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" required>
                    <option value="">اختر نوع المكافأة</option>
                    <?php foreach ($allowance_types as $type): ?>
                        <option value="<?php echo $type['id']; ?>" <?php echo $allowance['allowance_type_id'] == $type['id'] ? 'selected' : ''; ?> data-is-percentage="<?php echo $type['is_percentage']; ?>" data-default-value="<?php echo $type['default_value']; ?>">
                            <?php echo $type['name']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <!-- Amount -->
            <div>
                <label for="amount" class="block text-sm font-medium text-gray-700 mb-1">المبلغ <span class="text-red-600">*</span></label>
                <input type="number" id="amount" name="amount" value="<?php echo $allowance['amount']; ?>" step="0.01" min="0" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" required>
            </div>

            <!-- Is Percentage -->
            <div class="flex items-center">
                <label class="flex items-center">
                    <input type="checkbox" id="is_percentage" name="is_percentage" <?php echo $allowance['is_percentage'] ? 'checked' : ''; ?> class="rounded text-primary focus:ring-primary">
                    <span class="mr-2">نسبة مئوية من الراتب الأساسي</span>
                </label>
            </div>

            <!-- Effective Date -->
            <div>
                <label for="effective_date" class="block text-sm font-medium text-gray-700 mb-1">تاريخ البداية <span class="text-red-600">*</span></label>
                <input type="date" id="effective_date" name="effective_date" value="<?php echo $allowance['effective_date']; ?>" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" required>
            </div>

            <!-- End Date -->
            <div>
                <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">تاريخ النهاية</label>
                <input type="date" id="end_date" name="end_date" value="<?php echo $allowance['end_date']; ?>" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                <p class="text-xs text-gray-500 mt-1">افتراضيًا يكون نفس تاريخ البداية. اتركه فارغًا إذا كانت المكافأة مستمرة</p>
            </div>
        </div>

        <div class="flex justify-end">
            <button type="submit" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-md">
                <i class="fas fa-save ml-2"></i> حفظ المكافأة
            </button>
        </div>
    </form>
</div>

<!-- JavaScript for allowance type selection and date synchronization -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const allowanceTypeSelect = document.getElementById('allowance_type_id');
        const amountInput = document.getElementById('amount');
        const isPercentageCheckbox = document.getElementById('is_percentage');
        const effectiveDateInput = document.getElementById('effective_date');
        const endDateInput = document.getElementById('end_date');

        // Update allowance values when type changes
        allowanceTypeSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                const isPercentage = selectedOption.getAttribute('data-is-percentage') === '1';
                const defaultValue = parseFloat(selectedOption.getAttribute('data-default-value'));

                isPercentageCheckbox.checked = isPercentage;
                amountInput.value = defaultValue;
            }
        });

        // Update end date when effective date changes
        effectiveDateInput.addEventListener('change', function() {
            // Only update end date if it's empty or if it was the same as the previous effective date
            if (endDateInput.value === '' || endDateInput.getAttribute('data-synced') === 'true') {
                endDateInput.value = this.value;
                endDateInput.setAttribute('data-synced', 'true');
            }
        });

        // When end date is manually changed, mark it as no longer synced
        endDateInput.addEventListener('change', function() {
            if (this.value !== effectiveDateInput.value) {
                this.setAttribute('data-synced', 'false');
            } else {
                this.setAttribute('data-synced', 'true');
            }
        });

        // Initialize the synced state
        if (endDateInput.value === effectiveDateInput.value) {
            endDateInput.setAttribute('data-synced', 'true');
        } else {
            endDateInput.setAttribute('data-synced', 'false');
        }
    });
</script>

<?php
// Include footer
require_once '../includes/footer.php';
?>
