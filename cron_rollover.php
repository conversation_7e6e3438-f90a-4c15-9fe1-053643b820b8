<?php
// Cron job for processing holiday rollovers
// Run this script monthly to process holiday rollovers

require_once __DIR__ . "/includes/config.php";
require_once __DIR__ . "/includes/functions.php";

// Log function for cron
function cron_log($message) {
    $log_file = __DIR__ . "/logs/rollover_cron.log";
    $timestamp = date("Y-m-d H:i:s");
    file_put_contents($log_file, "[$timestamp] $message" . PHP_EOL, FILE_APPEND | LOCK_EX);
}

// Create logs directory if it doesn't exist
if (!file_exists(__DIR__ . "/logs")) {
    mkdir(__DIR__ . "/logs", 0755, true);
}

cron_log("Starting holiday rollover processing...");

// Get settings
$enable_rollover = get_setting("enable_holiday_rollover", "1");
$max_rollover_days = (int)get_setting("max_rollover_days", "10");
$rollover_expiry_months = (int)get_setting("rollover_expiry_months", "3");

if (!$enable_rollover) {
    cron_log("Holiday rollover is disabled. Exiting.");
    exit;
}

// Find attendance records on Fridays that haven't been processed
$sql = "SELECT a.*, e.name as employee_name 
        FROM attendance a 
        JOIN employees e ON a.employee_id = e.id 
        LEFT JOIN holiday_rollovers hr ON a.id = hr.attendance_id
        WHERE DAYOFWEEK(a.date) = 6 
        AND a.status = 'present'
        AND hr.id IS NULL
        AND a.date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
        ORDER BY a.date DESC";

$result = $conn->query($sql);

if (!$result) {
    cron_log("Error in query: " . $conn->error);
    exit;
}

$processed_count = 0;
$error_count = 0;

while ($row = $result->fetch_assoc()) {
    $employee_id = $row["employee_id"];
    $attendance_id = $row["id"];
    $attendance_date = $row["date"];
    $employee_name = $row["employee_name"];
    
    // Check current rollover days for employee
    $count_sql = "SELECT COUNT(*) as total FROM holiday_rollovers 
                 WHERE employee_id = ? AND is_used = 0 
                 AND expiry_date >= CURDATE()";
    $count_stmt = $conn->prepare($count_sql);
    $count_stmt->bind_param("i", $employee_id);
    $count_stmt->execute();
    $count_result = $count_stmt->get_result();
    $count_row = $count_result->fetch_assoc();
    $current_rollover_days = $count_row["total"];
    
    if ($current_rollover_days >= $max_rollover_days) {
        cron_log("Employee $employee_name has reached maximum rollover days ($max_rollover_days). Skipping $attendance_date.");
        continue;
    }
    
    // Calculate rollover date (first day of next month)
    $rollover_date = date("Y-m-01", strtotime("+1 month", strtotime($attendance_date)));
    
    // Calculate expiry date
    $expiry_date = date("Y-m-d", strtotime("+{$rollover_expiry_months} months", strtotime($rollover_date)));
    
    // Insert rollover record
    $insert_sql = "INSERT INTO holiday_rollovers 
                  (employee_id, attendance_id, rollover_date, expiry_date, is_used, created_at) 
                  VALUES (?, ?, ?, ?, 0, NOW())";
    $insert_stmt = $conn->prepare($insert_sql);
    $insert_stmt->bind_param("iiss", $employee_id, $attendance_id, $rollover_date, $expiry_date);
    
    if ($insert_stmt->execute()) {
        $processed_count++;
        cron_log("Successfully processed rollover for $employee_name on $attendance_date (expires: $expiry_date)");
        
        // Log activity
        log_activity("holiday_rollover", "تم ترحيل إجازة أسبوعية للموظف: $employee_name عن يوم $attendance_date");
    } else {
        $error_count++;
        cron_log("Error processing rollover for $employee_name on $attendance_date: " . $conn->error);
    }
}

cron_log("Rollover processing completed. Processed: $processed_count, Errors: $error_count");

// Clean up expired rollovers
$cleanup_sql = "DELETE FROM holiday_rollovers WHERE expiry_date < CURDATE() AND is_used = 0";
if ($conn->query($cleanup_sql)) {
    $deleted_count = $conn->affected_rows;
    cron_log("Cleaned up $deleted_count expired rollover records");
} else {
    cron_log("Error cleaning up expired rollovers: " . $conn->error);
}

$conn->close();
cron_log("Holiday rollover processing finished.");
?>