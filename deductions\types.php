<?php
/**
 * Deduction Types Management Page
 *
 * This file handles managing deduction types (أنواع الاستقطاعات)
 *
 * أنواع الاستقطاعات المقترحة:
 * - سلفة (مبلغ مقدم للموظف يتم استقطاعه من راتبه)
 * - خصم إهمال (خصم بسبب الإهمال في العمل)
 * - استقطاعات أخرى (أي استقطاعات أخرى)
 */

// Include header
require_once '../includes/header.php';

// Check if user has permission
if (!is_admin() && !is_supervisor()) {
    set_flash_message('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger');
    redirect('../index.php');
}

// Ensure table has required columns - simple approach
try {
    $conn->query("ALTER TABLE deduction_types ADD COLUMN description text DEFAULT NULL AFTER name");
} catch (Exception $e) {
    // Column probably exists, ignore
}

try {
    $conn->query("ALTER TABLE deduction_types ADD COLUMN default_value decimal(10,2) DEFAULT 0.00 AFTER is_percentage");
} catch (Exception $e) {
    // Column probably exists, ignore
}

// Get current table columns for display purposes
$table_columns = [];
$result = $conn->query("SHOW COLUMNS FROM deduction_types");
while ($row = $result->fetch_assoc()) {
    $table_columns[] = $row['Field'];
}

// Initialize variables
$deduction_type = [
    'id' => 0,
    'name' => ''
];
$errors = [];
$edit_mode = false;

// Process edit request
if (isset($_GET['edit']) && !empty($_GET['edit'])) {
    $deduction_type_id = (int)$_GET['edit'];

    // Get deduction type details
    $sql = "SELECT * FROM deduction_types WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $deduction_type_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $deduction_type = [
            'id' => $row['id'],
            'name' => $row['name']
        ];
        $edit_mode = true;
    }
}

// Process delete request
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $deduction_type_id = (int)$_GET['delete'];

    // Check if deduction type is used in any employee deduction
    $sql = "SELECT COUNT(*) as count FROM employee_deductions WHERE deduction_type_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $deduction_type_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();

    if ($row['count'] > 0) {
        set_flash_message('لا يمكن حذف نوع الاستقطاع لأنه مستخدم في استقطاعات الموظفين', 'danger');
    } else {
        // Delete deduction type
        $sql = "DELETE FROM deduction_types WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $deduction_type_id);

        if ($stmt->execute()) {
            // Log activity
            log_activity('delete_deduction_type', 'تم حذف نوع استقطاع');

            // Set success message
            set_flash_message('تم حذف نوع الاستقطاع بنجاح', 'success');
        } else {
            // Set error message
            set_flash_message('حدث خطأ أثناء حذف نوع الاستقطاع', 'danger');
        }
    }

    // Redirect to avoid resubmission
    redirect('types.php');
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
        $errors['csrf'] = 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.';
    } else {
        // Get and sanitize input
        $deduction_type['id'] = isset($_POST['id']) ? (int)$_POST['id'] : 0;
        $deduction_type['name'] = isset($_POST['name']) ? sanitize($_POST['name']) : '';

        // Validate input
        if (empty($deduction_type['name'])) {
            $errors['name'] = 'يرجى إدخال اسم نوع الاستقطاع.';
        }

        // If no errors, save deduction type
        if (empty($errors)) {
            if ($deduction_type['id'] > 0) {
                // Update existing deduction type - name only
                $sql = "UPDATE deduction_types SET name = ?, updated_at = NOW() WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("si",
                    $deduction_type['name'],
                    $deduction_type['id']
                );

                if ($stmt->execute()) {
                    // Log activity
                    log_activity('update_deduction_type', 'تم تحديث نوع استقطاع: ' . $deduction_type['name']);

                    // Set success message
                    set_flash_message('تم تحديث نوع الاستقطاع بنجاح', 'success');

                    // Reset form
                    $deduction_type = [
                        'id' => 0,
                        'name' => ''
                    ];
                    $edit_mode = false;
                } else {
                    $errors['db'] = 'حدث خطأ أثناء تحديث نوع الاستقطاع. يرجى المحاولة مرة أخرى.';
                }
            } else {
                // Insert new deduction type - name only
                $sql = "INSERT INTO deduction_types (name, created_at) VALUES (?, NOW())";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("s", $deduction_type['name']);

                if ($stmt->execute()) {
                    // Log activity
                    log_activity('add_deduction_type', 'تم إضافة نوع استقطاع جديد: ' . $deduction_type['name']);

                    // Set success message
                    set_flash_message('تم إضافة نوع الاستقطاع بنجاح', 'success');

                    // Reset form
                    $deduction_type = [
                        'id' => 0,
                        'name' => ''
                    ];
                } else {
                    $errors['db'] = 'حدث خطأ أثناء إضافة نوع الاستقطاع. يرجى المحاولة مرة أخرى.';
                }
            }

            // Redirect to avoid resubmission
            redirect('types.php');
        }
    }
}

// Get all deduction types
$sql = "SELECT * FROM deduction_types ORDER BY name";
$result = $conn->query($sql);
$deduction_types = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $deduction_types[] = $row;
    }
}

// Generate CSRF token
$csrf_token = generate_csrf_token();
?>

<div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold">إدارة أنواع الاستقطاعات</h1>
    <div class="flex space-x-2 space-x-reverse">
        <a href="index.php" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-md flex items-center">
            <i class="fas fa-list ml-2"></i> قائمة الاستقطاعات
        </a>
        <a href="../index.php" class="bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-md flex items-center">
            <i class="fas fa-arrow-right ml-2"></i> العودة إلى لوحة التحكم
        </a>
    </div>
</div>

<?php if (!empty($errors)): ?>
    <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 mb-6 rounded-md">
        <ul class="list-disc list-inside">
            <?php foreach ($errors as $error): ?>
                <li><?php echo $error; ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- Deduction Types Form -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4"><?php echo $edit_mode ? 'تعديل نوع الاستقطاع' : 'إضافة نوع استقطاع جديد'; ?></h2>
        <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
            <input type="hidden" name="id" value="<?php echo $deduction_type['id']; ?>">

            <div class="mb-4">
                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">الاسم <span class="text-red-600">*</span></label>
                <input type="text" id="name" name="name" value="<?php echo $deduction_type['name']; ?>" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" required>
            </div>

            <!-- تم تبسيط النموذج ليحتوي على الاسم فقط -->

            <div class="flex justify-end">
                <?php if ($edit_mode): ?>
                    <a href="types.php" class="bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-md ml-2">
                        إلغاء
                    </a>
                <?php endif; ?>
                <button type="submit" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-md">
                    <i class="fas fa-save ml-2"></i> <?php echo $edit_mode ? 'تحديث' : 'إضافة'; ?>
                </button>
            </div>
        </form>
    </div>

    <!-- Deduction Types List -->
    <div class="bg-white rounded-lg shadow-md p-6 md:col-span-2">
        <h2 class="text-xl font-semibold mb-4">أنواع الاستقطاعات</h2>

        <?php if (empty($deduction_types)): ?>
            <p class="text-gray-500 text-center py-4">لا توجد أنواع استقطاعات</p>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                الاسم
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                الإجراءات
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($deduction_types as $type): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="font-medium"><?php echo $type['name']; ?></span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <a href="types.php?edit=<?php echo $type['id']; ?>" class="text-yellow-600 hover:text-yellow-900 ml-2">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="types.php?delete=<?php echo $type['id']; ?>" class="text-red-600 hover:text-red-900 ml-2" onclick="return confirm('هل أنت متأكد من حذف هذا النوع؟');">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// Include footer
require_once '../includes/footer.php';
?>
