/**
 * Table Enhancements Script
 *
 * This script adds additional enhancements to tables in the system
 */

document.addEventListener('DOMContentLoaded', function() {
    // Apply enhanced table styles to all tables
    enhanceAllTables();
    
    // Add export buttons to tables with export-table class
    addExportButtons();
    
    // Add print buttons to tables with print-table class
    addPrintButtons();
});

/**
 * Apply enhanced table styles to all tables
 */
function enhanceAllTables() {
    // Find all tables that don't have the no-enhance class
    const tables = document.querySelectorAll('table:not(.no-enhance)');
    
    tables.forEach(function(table) {
        // Add thin borders to all cells
        const cells = table.querySelectorAll('th, td');
        cells.forEach(function(cell) {
            cell.style.border = '1px solid #e5e7eb';
        });
        
        // Make sure all tables have consistent font size
        table.style.fontSize = '0.875rem';
        
        // Add hover effect to rows
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(function(row) {
            row.addEventListener('mouseover', function() {
                this.style.backgroundColor = 'rgba(255, 107, 0, 0.05)';
            });
            
            row.addEventListener('mouseout', function() {
                this.style.backgroundColor = '';
            });
        });
        
        // Add zebra striping
        const bodyRows = table.querySelectorAll('tbody tr');
        bodyRows.forEach(function(row, index) {
            if (index % 2 === 1) {
                row.style.backgroundColor = '#f9fafb';
            }
        });
        
        // Add header styling
        const headerCells = table.querySelectorAll('thead th');
        headerCells.forEach(function(cell) {
            cell.style.backgroundColor = '#f8fafc';
            cell.style.fontWeight = '600';
            cell.style.color = '#1e293b';
            cell.style.textAlign = 'right';
        });
    });
}

/**
 * Add export buttons to tables with export-table class
 */
function addExportButtons() {
    const exportTables = document.querySelectorAll('table.export-table');
    
    exportTables.forEach(function(table, index) {
        // Create export button container
        const buttonContainer = document.createElement('div');
        buttonContainer.classList.add('flex', 'justify-end', 'mb-4');
        
        // Create Excel export button
        const excelButton = document.createElement('button');
        excelButton.classList.add('bg-green-600', 'hover:bg-green-700', 'text-white', 'py-2', 'px-4', 'rounded-md', 'flex', 'items-center', 'text-sm', 'ml-2');
        excelButton.innerHTML = '<i class="fas fa-file-excel ml-2"></i> تصدير إلى Excel';
        excelButton.addEventListener('click', function() {
            exportTableToExcel(table, 'table_export_' + index);
        });
        
        // Add buttons to container
        buttonContainer.appendChild(excelButton);
        
        // Add container before table
        table.parentNode.insertBefore(buttonContainer, table);
    });
}

/**
 * Export table to Excel
 * 
 * @param {HTMLElement} table Table element to export
 * @param {string} filename Filename for the exported file
 */
function exportTableToExcel(table, filename) {
    // Create a workbook with a worksheet
    const wb = XLSX.utils.book_new();
    
    // Convert table to worksheet
    const ws = XLSX.utils.table_to_sheet(table, {
        raw: true,
        dateNF: 'yyyy-mm-dd'
    });
    
    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    
    // Generate Excel file and trigger download
    XLSX.writeFile(wb, filename + '.xlsx');
}

/**
 * Add print buttons to tables with print-table class
 */
function addPrintButtons() {
    const printTables = document.querySelectorAll('table.print-table');
    
    printTables.forEach(function(table) {
        // Create print button container
        const buttonContainer = document.createElement('div');
        buttonContainer.classList.add('flex', 'justify-end', 'mb-4');
        
        // Create print button
        const printButton = document.createElement('button');
        printButton.classList.add('bg-blue-600', 'hover:bg-blue-700', 'text-white', 'py-2', 'px-4', 'rounded-md', 'flex', 'items-center', 'text-sm');
        printButton.innerHTML = '<i class="fas fa-print ml-2"></i> طباعة';
        printButton.addEventListener('click', function() {
            printTable(table);
        });
        
        // Add button to container
        buttonContainer.appendChild(printButton);
        
        // Add container before table
        table.parentNode.insertBefore(buttonContainer, table);
    });
}

/**
 * Print table
 * 
 * @param {HTMLElement} table Table element to print
 */
function printTable(table) {
    // Create a new window
    const printWindow = window.open('', '_blank');
    
    // Get the table HTML
    const tableHTML = table.outerHTML;
    
    // Create print document
    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>طباعة الجدول</title>
            <style>
                body {
                    font-family: 'Cairo', Arial, sans-serif;
                    direction: rtl;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 1rem;
                }
                table th, table td {
                    padding: 0.75rem;
                    border: 1px solid #e2e8f0;
                    text-align: right;
                }
                table th {
                    background-color: #f8fafc;
                    font-weight: 600;
                    color: #1e293b;
                }
                table tr:nth-child(even) {
                    background-color: #f9fafb;
                }
                @media print {
                    body {
                        padding: 20px;
                    }
                }
            </style>
        </head>
        <body>
            <h1>طباعة الجدول</h1>
            ${tableHTML}
        </body>
        </html>
    `);
    
    // Wait for content to load then print
    printWindow.document.close();
    printWindow.onload = function() {
        printWindow.print();
    };
}
