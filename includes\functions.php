<?php
/**
 * Common Functions File
 *
 * This file contains common functions used throughout the Employee Management System
 */

// Include configuration file
require_once 'config.php';

/**
 * Sanitize input data to prevent XSS attacks
 *
 * @param string $data Input data to sanitize
 * @return string Sanitized data
 */
function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * Validate and sanitize input
 *
 * @param string $input Input data
 * @param string $type Type of validation (text, email, number, date, etc.)
 * @return mixed Sanitized data or false if validation fails
 */
function validate_input($input, $type = 'text') {
    $input = sanitize($input);

    switch ($type) {
        case 'email':
            if (!filter_var($input, FILTER_VALIDATE_EMAIL)) {
                return false;
            }
            break;
        case 'number':
            if (!is_numeric($input)) {
                return false;
            }
            break;
        case 'integer':
            if (!filter_var($input, FILTER_VALIDATE_INT)) {
                return false;
            }
            break;
        case 'float':
            if (!filter_var($input, FILTER_VALIDATE_FLOAT)) {
                return false;
            }
            break;
        case 'date':
            $date = DateTime::createFromFormat('Y-m-d', $input);
            if (!$date || $date->format('Y-m-d') !== $input) {
                return false;
            }
            break;
        case 'time':
            $time = DateTime::createFromFormat('H:i:s', $input);
            if (!$time || $time->format('H:i:s') !== $input) {
                return false;
            }
            break;
        case 'datetime':
            $datetime = DateTime::createFromFormat('Y-m-d H:i:s', $input);
            if (!$datetime || $datetime->format('Y-m-d H:i:s') !== $input) {
                return false;
            }
            break;
        case 'url':
            if (!filter_var($input, FILTER_VALIDATE_URL)) {
                return false;
            }
            break;
        case 'phone':
            // تحقق من رقم الهاتف (يقبل الأرقام والرموز + - () مع 8-15 رقم)
            if (!preg_match('/^[0-9()+\-\s]{8,15}$/', $input)) {
                return false;
            }
            break;
        case 'username':
            // تحقق من اسم المستخدم (يقبل الحروف والأرقام والشرطة السفلية مع 3-20 حرف)
            if (!preg_match('/^[a-zA-Z0-9_]{3,20}$/', $input)) {
                return false;
            }
            break;
        case 'password':
            // تحقق من كلمة المرور (يجب أن تحتوي على 8 أحرف على الأقل)
            if (strlen($input) < 8) {
                return false;
            }
            break;
        case 'name':
            // تحقق من الاسم (يقبل الحروف العربية والإنجليزية والمسافات)
            if (!preg_match('/^[\p{Arabic}a-zA-Z\s]{2,100}$/u', $input)) {
                return false;
            }
            break;
    }

    return $input;
}
/**
 * التحقق من مجموعة من المدخلات دفعة واحدة
 *
 * @param array $inputs مصفوفة من المدخلات للتحقق منها
 * @param array $types مصفوفة من أنواع التحقق المقابلة للمدخلات
 * @return array مصفوفة من المدخلات المصفاة أو false إذا فشل التحقق
 */
function validate_inputs($inputs, $types) {
    $validated = [];
    $errors = [];

    foreach ($inputs as $key => $value) {
        $type = isset($types[$key]) ? $types[$key] : 'text';
        $result = validate_input($value, $type);

        if ($result === false) {
            $errors[$key] = 'قيمة غير صالحة لحقل ' . $key;
        } else {
            $validated[$key] = $result;
        }
    }

    if (!empty($errors)) {
        return ['status' => false, 'errors' => $errors];
    }

    return ['status' => true, 'data' => $validated];
}



/**
 * Generate a secure password hash
 *
 * @param string $password Plain text password
 * @return string Hashed password
 */
function password_hash_custom($password) {
    // استخدام خوارزمية Argon2id الأكثر أمانًا (إذا كانت متاحة)
    if (defined('PASSWORD_ARGON2ID')) {
        return password_hash($password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536, // 64 ميجابايت
            'time_cost' => 4,       // 4 تكرارات
            'threads' => 2          // 2 مؤشر ترابط
        ]);
    }
    // استخدام خوارزمية Argon2i كبديل (إذا كانت متاحة)
    else if (defined('PASSWORD_ARGON2I')) {
        return password_hash($password, PASSWORD_ARGON2I, [
            'memory_cost' => 65536,
            'time_cost' => 4,
            'threads' => 2
        ]);
    }
    // استخدام خوارزمية BCRYPT كملاذ أخير
    else {
        return password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
    }
}


/**
 * التحقق من قوة كلمة المرور
 *
 * @param string $password كلمة المرور للتحقق منها
 * @return array حالة التحقق ودرجة القوة والملاحظات
 */
function check_password_strength($password) {
    $strength = 0;
    $feedback = [];

    // التحقق من الطول
    if (strlen($password) < 8) {
        $feedback[] = 'كلمة المرور قصيرة جدًا (يجب أن تكون 8 أحرف على الأقل)';
    } else {
        $strength += 1;
    }

    // التحقق من وجود أحرف كبيرة
    if (preg_match('/[A-Z]/', $password)) {
        $strength += 1;
    } else {
        $feedback[] = 'يجب أن تحتوي كلمة المرور على حرف كبير واحد على الأقل';
    }

    // التحقق من وجود أحرف صغيرة
    if (preg_match('/[a-z]/', $password)) {
        $strength += 1;
    } else {
        $feedback[] = 'يجب أن تحتوي كلمة المرور على حرف صغير واحد على الأقل';
    }

    // التحقق من وجود أرقام
    if (preg_match('/[0-9]/', $password)) {
        $strength += 1;
    } else {
        $feedback[] = 'يجب أن تحتوي كلمة المرور على رقم واحد على الأقل';
    }

    // التحقق من وجود رموز خاصة
    if (preg_match('/[^a-zA-Z0-9]/', $password)) {
        $strength += 1;
    } else {
        $feedback[] = 'يجب أن تحتوي كلمة المرور على رمز خاص واحد على الأقل';
    }

    // تحديد مستوى القوة
    $strength_level = '';
    if ($strength <= 2) {
        $strength_level = 'ضعيفة';
    } else if ($strength <= 3) {
        $strength_level = 'متوسطة';
    } else if ($strength <= 4) {
        $strength_level = 'قوية';
    } else {
        $strength_level = 'قوية جدًا';
    }

    return [
        'status' => $strength >= 3, // قبول كلمة المرور إذا كانت متوسطة القوة على الأقل
        'strength' => $strength,
        'level' => $strength_level,
        'feedback' => $feedback
    ];
}

/**
 * Verify password against hash
 *
 * @param string $password Plain text password
 * @param string $hash Hashed password
 * @return bool True if password matches hash
 */
function password_verify_custom($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Generate a CSRF token
 *
 * @return string CSRF token
 */
function generate_csrf_token() {
    global $conn;

    // إنشاء رمز CSRF جديد
    $token = bin2hex(random_bytes(32));
    $session_id = session_id();
    $expires_at = date('Y-m-d H:i:s', time() + 3600); // صلاحية لمدة ساعة

    // التحقق من وجود جدول csrf_tokens
    $table_exists = false;
    try {
        $check_table = $conn->query("SHOW TABLES LIKE 'csrf_tokens'");
        $table_exists = ($check_table && $check_table->num_rows > 0);
    } catch (Exception $e) {
        // تجاهل الخطأ واستخدام الجلسة فقط
        error_log("CSRF table check error: " . $e->getMessage());
    }

    if ($table_exists) {
        try {
            // حذف الرموز منتهية الصلاحية
            $delete_expired = "DELETE FROM csrf_tokens WHERE expires_at < NOW()";
            $conn->query($delete_expired);

            // إضافة الرمز الجديد إلى قاعدة البيانات
            $insert_token = "INSERT INTO csrf_tokens (token, session_id, expires_at) VALUES (?, ?, ?)";
            $stmt = $conn->prepare($insert_token);
            $stmt->bind_param("sss", $token, $session_id, $expires_at);
            $stmt->execute();
        } catch (Exception $e) {
            // تجاهل الخطأ واستخدام الجلسة فقط
            error_log("CSRF token insert error: " . $e->getMessage());
        }
    }

    // تخزين الرمز في الجلسة أيضًا للتوافق مع الكود القديم
    $_SESSION['csrf_token'] = $token;

    return $token;
}

/**
 * Verify CSRF token
 *
 * @param string $token Token to verify
 * @return bool True if token is valid
 */
function verify_csrf_token($token) {
    global $conn;

    // التحقق من وجود الرمز في الجلسة (للتوافق مع الكود القديم)
    if (isset($_SESSION['csrf_token']) && $token === $_SESSION['csrf_token']) {
        // الرمز صحيح في الجلسة
        $_SESSION['csrf_token'] = generate_csrf_token(); // إنشاء رمز جديد
        return true;
    }

    // التحقق من وجود جدول csrf_tokens
    $table_exists = false;
    try {
        $check_table = $conn->query("SHOW TABLES LIKE 'csrf_tokens'");
        $table_exists = ($check_table && $check_table->num_rows > 0);
    } catch (Exception $e) {
        // تجاهل الخطأ
        error_log("CSRF table check error in verify_csrf_token: " . $e->getMessage());
    }

    if ($table_exists) {
        try {
            // التحقق من وجود الرمز في قاعدة البيانات
            $session_id = session_id();
            $check_token = "SELECT id FROM csrf_tokens WHERE token = ? AND session_id = ? AND expires_at > NOW()";
            $stmt = $conn->prepare($check_token);
            $stmt->bind_param("ss", $token, $session_id);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                // حذف الرمز المستخدم
                $delete_token = "DELETE FROM csrf_tokens WHERE token = ?";
                $stmt = $conn->prepare($delete_token);
                $stmt->bind_param("s", $token);
                $stmt->execute();

                // إنشاء رمز جديد
                $_SESSION['csrf_token'] = generate_csrf_token();
                return true;
            }
        } catch (Exception $e) {
            // تجاهل الخطأ
            error_log("CSRF token verification error: " . $e->getMessage());
        }
    }

    // إنشاء رمز جديد في حالة الفشل
    $_SESSION['csrf_token'] = generate_csrf_token();

    return false;
}

/**
 * Redirect to a URL
 *
 * @param string $url URL to redirect to
 * @return void
 */
function redirect($url) {
    // Get the base URL from the server
    $base_url = get_base_url();

    // Check if URL starts with http:// or https:// (absolute URL)
    if (strpos($url, 'http://') === 0 || strpos($url, 'https://') === 0) {
        // URL is already absolute, use as is
    } elseif (strpos($url, '/') === 0) {
        // URL starts with /, it's a root-relative URL
        // Remove leading slash to avoid double slash
        $url = ltrim($url, '/');
        $url = $base_url . $url;
    } elseif (strpos($url, '../') === 0) {
        // URL starts with ../, it's a relative URL going up one level
        // Replace ../ with the base URL
        $url = $base_url . substr($url, 3);
    } else {
        // URL is relative, append to base URL directly
        $url = $base_url . $url;
    }

    header("Location: $url");
    exit;
}

/**
 * Get the base URL of the application
 *
 * @return string Base URL with trailing slash
 */
function get_base_url() {
    // Get the protocol
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';

    // Get the host with port
    $host = $_SERVER['HTTP_HOST'];

    // For development server, always return the root
    if (strpos($host, 'localhost:8000') !== false) {
        return $protocol . $host . '/';
    }

    // Get the script name and extract the directory
    $script_name = $_SERVER['SCRIPT_NAME'];
    $base_path = dirname($script_name);

    // If we're in a subdirectory of the document root, get that path
    if ($base_path !== '/' && $base_path !== '\\') {
        // Ensure the path ends with a slash
        $base_path = rtrim($base_path, '/') . '/';
    } else {
        $base_path = '/';
    }

    // Construct the base URL
    return $protocol . $host . $base_path;
}

/**
 * Check if user is logged in
 *
 * @return bool True if user is logged in
 */
function is_logged_in() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Check if user has admin role
 *
 * @return bool True if user has admin role
 */
function is_admin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

/**
 * Format date according to system settings
 *
 * @param string $date Date to format
 * @param string $format Format to use (default: system date format)
 * @return string Formatted date
 */
function format_date($date, $format = null) {
    // التعامل مع القيم الفارغة أو الصفرية
    if (empty($date) || $date == '0000-00-00' || $date == '0000-00-00 00:00:00') {
        return '';
    }

    // تحديد تنسيق التاريخ المطلوب
    if ($format === null) {
        $format = defined('DATE_FORMAT') ? DATE_FORMAT : get_setting('date_format', 'd/m/Y');
    }

    try {
        // محاولة تحويل التاريخ إلى كائن DateTime
        $date_obj = new DateTime($date);

        // تنسيق التاريخ
        $formatted_date = $date_obj->format($format);

        // استبدال الأرقام الإنجليزية بالأرقام العربية إذا كان مطلوباً
        $arabic_digits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        $english_digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

        if (get_setting('use_arabic_digits', '0') === '1') {
            $formatted_date = str_replace($english_digits, $arabic_digits, $formatted_date);
        }

        return $formatted_date;
    } catch (Exception $e) {
        // محاولة تحليل التاريخ باستخدام strtotime
        if (strtotime($date) !== false) {
            try {
                $formatted_date = date($format, strtotime($date));

                // استبدال الأرقام الإنجليزية بالأرقام العربية إذا كان مطلوباً
                if (get_setting('use_arabic_digits', '0') === '1') {
                    $arabic_digits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
                    $english_digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
                    $formatted_date = str_replace($english_digits, $arabic_digits, $formatted_date);
                }

                return $formatted_date;
            } catch (Exception $inner_e) {
                // تجاهل الخطأ والاستمرار
            }
        }

        // محاولة تحليل التاريخ بتنسيقات مختلفة
        $possible_formats = [
            'd/m/Y', // 31/12/2023
            'm/d/Y', // 12/31/2023
            'Y-m-d', // 2023-12-31
            'd-m-Y', // 31-12-2023
            'm-d-Y', // 12-31-2023
            'd.m.Y', // 31.12.2023
            'Y.m.d'  // 2023.12.31
        ];

        foreach ($possible_formats as $try_format) {
            $d = DateTime::createFromFormat($try_format, $date);
            if ($d && $d->format($try_format) == $date) {
                try {
                    $formatted_date = $d->format($format);

                    // استبدال الأرقام الإنجليزية بالأرقام العربية إذا كان مطلوباً
                    if (get_setting('use_arabic_digits', '0') === '1') {
                        $arabic_digits = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
                        $english_digits = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
                        $formatted_date = str_replace($english_digits, $arabic_digits, $formatted_date);
                    }

                    return $formatted_date;
                } catch (Exception $inner_e) {
                    // تجاهل الخطأ والاستمرار
                }
            }
        }

        // إذا وصلنا إلى هنا، فلم نتمكن من تحليل التاريخ بأي تنسيق
        // نعيد التاريخ الأصلي
        return $date;
    }
}

/**
 * Format time according to system settings
 *
 * @param string $time Time to format
 * @param string $format Format to use (default: system time format)
 * @return string Formatted time
 */
function format_time($time, $format = null) {
    if (empty($time)) {
        return '';
    }

    if ($format === null) {
        $format = defined('TIME_FORMAT') ? TIME_FORMAT : get_setting('time_format', 'H:i:s');
    }

    $time_obj = new DateTime($time);
    return $time_obj->format($format);
}

/**
 * Format datetime according to system settings
 *
 * @param string $datetime Datetime to format
 * @param string $format Format to use (default: system datetime format)
 * @return string Formatted datetime
 */
function format_datetime($datetime, $format = null) {
    if (empty($datetime)) {
        return '';
    }

    if ($format === null) {
        $date_format = defined('DATE_FORMAT') ? DATE_FORMAT : get_setting('date_format', 'Y-m-d');
        $time_format = defined('TIME_FORMAT') ? TIME_FORMAT : get_setting('time_format', 'H:i:s');
        $format = $date_format . ' ' . $time_format;
    }

    $datetime_obj = new DateTime($datetime);
    return $datetime_obj->format($format);
}

/**
 * Format currency according to system settings
 *
 * @param float $amount Amount to format
 * @return string Formatted amount
 */
function format_currency($amount) {
    $currency_symbol = defined('CURRENCY_SYMBOL') ? CURRENCY_SYMBOL : get_setting('currency_symbol', 'ر.س');
    return number_format($amount, 2) . ' ' . $currency_symbol;
}

/**
 * Get current date and time
 *
 * @param string $format Format to use (default: 'Y-m-d H:i:s')
 * @return string Current date and time
 */
function current_datetime($format = 'Y-m-d H:i:s') {
    $date = new DateTime();
    return $date->format($format);
}

/**
 * Log system activity
 *
 * @param string $action Action performed
 * @param string $description Description of the action
 * @param int $user_id ID of the user who performed the action
 * @return bool True if log was created successfully
 */
function log_activity($action, $description, $user_id = null) {
    global $conn;

    if ($user_id === null && isset($_SESSION['user_id'])) {
        $user_id = $_SESSION['user_id'];
    }

    $action = sanitize($action);
    $description = sanitize($description);
    $user_id = (int)$user_id;
    $timestamp = current_datetime();
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $page_url = $_SERVER['REQUEST_URI'] ?? '';

    // التحقق من وجود المستخدم قبل تسجيل النشاط
    if ($user_id > 0) {
        $check_sql = "SELECT id FROM users WHERE id = ?";
        $check_stmt = $conn->prepare($check_sql);
        $check_stmt->bind_param("i", $user_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows === 0) {
            // المستخدم غير موجود، لا يمكن تسجيل النشاط
            return false;
        }
    }

    try {
        // التحقق من وجود جدول سجل النشاط
        $check_table = $conn->query("SHOW TABLES LIKE 'activity_log'");
        if ($check_table->num_rows === 0) {
            // إنشاء جدول سجل النشاط إذا لم يكن موجودًا
            $create_table = "CREATE TABLE IF NOT EXISTS `activity_log` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) DEFAULT NULL,
                `action` varchar(50) NOT NULL,
                `description` text NOT NULL,
                `ip_address` varchar(45) DEFAULT NULL,
                `user_agent` varchar(255) DEFAULT NULL,
                `page_url` varchar(255) DEFAULT NULL,
                `timestamp` datetime NOT NULL,
                PRIMARY KEY (`id`),
                KEY `user_id` (`user_id`),
                KEY `action` (`action`),
                KEY `timestamp` (`timestamp`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            $conn->query($create_table);
        }

        // التحقق من وجود الأعمدة الجديدة
        $check_columns = $conn->query("SHOW COLUMNS FROM `activity_log` LIKE 'ip_address'");
        if ($check_columns->num_rows === 0) {
            // إضافة الأعمدة الجديدة
            $alter_table = "ALTER TABLE `activity_log`
                ADD COLUMN `ip_address` varchar(45) DEFAULT NULL AFTER `description`,
                ADD COLUMN `user_agent` varchar(255) DEFAULT NULL AFTER `ip_address`,
                ADD COLUMN `page_url` varchar(255) DEFAULT NULL AFTER `user_agent`";

            $conn->query($alter_table);
        }

        // تسجيل النشاط
        $sql = "INSERT INTO activity_log (user_id, action, description, ip_address, user_agent, page_url, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?)";

        $stmt = $conn->prepare($sql);
        $stmt->bind_param("issssss", $user_id, $action, $description, $ip_address, $user_agent, $page_url, $timestamp);

        return $stmt->execute();
    } catch (Exception $e) {
        // تجاهل الأخطاء في تسجيل النشاط
        error_log("Error logging activity: " . $e->getMessage());
        return false;
    }
}

/**
 * Display alert message
 *
 * @param string $message Message to display
 * @param string $type Type of alert (success, danger, warning, info)
 * @return string HTML for alert message
 */
function display_alert($message, $type = 'info') {
    $color_classes = [
        'success' => 'bg-green-100 border-green-400 text-green-700',
        'danger' => 'bg-red-100 border-red-400 text-red-700',
        'warning' => 'bg-yellow-100 border-yellow-400 text-yellow-700',
        'info' => 'bg-blue-100 border-blue-400 text-blue-700'
    ];

    $color_class = isset($color_classes[$type]) ? $color_classes[$type] : $color_classes['info'];

    return '<div class="' . $color_class . ' border px-4 py-3 rounded mb-4" role="alert">
                <span class="block sm:inline">' . $message . '</span>
            </div>';
}

/**
 * Set flash message
 *
 * @param string $message Message to display
 * @param string $type Type of alert (success, danger, warning, info)
 * @return void
 */
function set_flash_message($message, $type = 'info') {
    $_SESSION['flash_message'] = [
        'message' => $message,
        'type' => $type
    ];
}

/**
 * Display flash message
 *
 * @return string HTML for flash message
 */
function display_flash_message() {
    if (isset($_SESSION['flash_message'])) {
        $flash = $_SESSION['flash_message'];
        unset($_SESSION['flash_message']);
        return display_alert($flash['message'], $flash['type']);
    }
    return '';
}

/**
 * Check if user has supervisor role
 *
 * @return bool True if user has supervisor role
 */
function is_supervisor() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'supervisor';
}

/**
 * التحقق مما إذا كان المستخدم لديه صلاحية محددة
 *
 * @param string $permission_name اسم الصلاحية للتحقق منها
 * @param int $user_id معرف المستخدم (اختياري، يستخدم المستخدم الحالي إذا لم يتم تحديده)
 * @return bool صحيح إذا كان المستخدم لديه الصلاحية
 */
function has_permission($permission_name, $user_id = null) {
    global $conn;

    // إذا لم يتم تحديد معرف المستخدم، استخدم المستخدم الحالي
    if ($user_id === null) {
        if (!isset($_SESSION['user_id'])) {
            return false; // المستخدم غير مسجل الدخول
        }
        $user_id = $_SESSION['user_id'];
    }

    // المدير لديه جميع الصلاحيات (إذا كان المستخدم الحالي)
    if ($user_id == $_SESSION['user_id'] && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin') {
        return true;
    }

    // التحقق من وجود جداول الصلاحيات
    try {
        $check_table = $conn->query("SHOW TABLES LIKE 'permissions'");
        if ($check_table->num_rows === 0) {
            return false; // جدول الصلاحيات غير موجود
        }

        $check_table = $conn->query("SHOW TABLES LIKE 'role_permissions'");
        if ($check_table->num_rows === 0) {
            return false; // جدول صلاحيات الأدوار غير موجود
        }
    } catch (Exception $e) {
        error_log("Error checking permissions tables: " . $e->getMessage());
        return false;
    }

    try {
        // الحصول على معلومات المستخدم
        $get_user_sql = "SELECT role, use_custom_permissions FROM users WHERE id = ?";
        $user_stmt = $conn->prepare($get_user_sql);
        $user_stmt->bind_param("i", $user_id);
        $user_stmt->execute();
        $user_result = $user_stmt->get_result();

        if ($user_result->num_rows === 0) {
            return false; // المستخدم غير موجود
        }

        $user_data = $user_result->fetch_assoc();
        $user_role = $user_data['role'];
        $use_custom_permissions = isset($user_data['use_custom_permissions']) ? (bool)$user_data['use_custom_permissions'] : false;

        // إذا كان المستخدم مدير، فلديه جميع الصلاحيات
        if ($user_role === 'admin' && !$use_custom_permissions) {
            return true;
        }

        // التحقق من الصلاحيات المخصصة للمستخدم إذا كان يستخدمها
        if ($use_custom_permissions) {
            // التحقق من وجود جدول صلاحيات المستخدمين المخصصة
            $check_table = $conn->query("SHOW TABLES LIKE 'user_permissions'");
            if ($check_table->num_rows > 0) {
                // التحقق من وجود الصلاحية المخصصة للمستخدم
                $check_custom_permission_sql = "SELECT up.id
                                              FROM user_permissions up
                                              JOIN permissions p ON up.permission_id = p.id
                                              WHERE up.user_id = ? AND p.name = ?";
                $custom_perm_stmt = $conn->prepare($check_custom_permission_sql);
                $custom_perm_stmt->bind_param("is", $user_id, $permission_name);
                $custom_perm_stmt->execute();
                $custom_perm_result = $custom_perm_stmt->get_result();

                return $custom_perm_result->num_rows > 0;
            }
        }

        // التحقق من وجود الصلاحية للدور
        $check_permission_sql = "SELECT rp.id
                                FROM role_permissions rp
                                JOIN permissions p ON rp.permission_id = p.id
                                WHERE rp.role = ? AND p.name = ?";
        $perm_stmt = $conn->prepare($check_permission_sql);
        $perm_stmt->bind_param("ss", $user_role, $permission_name);
        $perm_stmt->execute();
        $perm_result = $perm_stmt->get_result();

        return $perm_result->num_rows > 0;
    } catch (Exception $e) {
        error_log("Error checking permission: " . $e->getMessage());
        return false;
    }
}

/**
 * Get employee ID by user ID
 *
 * @param int $user_id User ID
 * @return int Employee ID or 0 if not found
 */
function get_employee_id_by_user_id($user_id) {
    global $conn;

    // First try to find by user_id column
    $sql = "SELECT id FROM employees WHERE user_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $row = $result->fetch_assoc()) {
        return $row['id'];
    }

    // If not found, try to find by matching user name with employee name
    $user_sql = "SELECT name FROM users WHERE id = ?";
    $user_stmt = $conn->prepare($user_sql);
    $user_stmt->bind_param("i", $user_id);
    $user_stmt->execute();
    $user_result = $user_stmt->get_result();

    if ($user_result && $user_row = $user_result->fetch_assoc()) {
        $emp_sql = "SELECT id FROM employees WHERE name = ? AND status = 'active' LIMIT 1";
        $emp_stmt = $conn->prepare($emp_sql);
        $emp_stmt->bind_param("s", $user_row['name']);
        $emp_stmt->execute();
        $emp_result = $emp_stmt->get_result();

        if ($emp_result && $emp_row = $emp_result->fetch_assoc()) {
            return $emp_row['id'];
        }
    }

    return 0;
}

/**
 * Get user ID by employee ID
 *
 * @param int $employee_id Employee ID
 * @return int User ID or 0 if not found
 */
function get_user_id_by_employee_id($employee_id) {
    global $conn;

    $sql = "SELECT user_id FROM employees WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $employee_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $row = $result->fetch_assoc()) {
        return $row['user_id'];
    }

    return 0;
}

/**
 * Get month name in Arabic
 *
 * @param int $month Month number (1-12)
 * @return string Month name in Arabic
 */
function get_month_name($month) {
    $months = [
        1 => 'يناير',
        2 => 'فبراير',
        3 => 'مارس',
        4 => 'أبريل',
        5 => 'مايو',
        6 => 'يونيو',
        7 => 'يوليو',
        8 => 'أغسطس',
        9 => 'سبتمبر',
        10 => 'أكتوبر',
        11 => 'نوفمبر',
        12 => 'ديسمبر'
    ];

    return isset($months[$month]) ? $months[$month] : '';
}

/**
 * Get setting value from database
 *
 * @param string $key Setting key
 * @param mixed $default Default value if setting not found
 * @return mixed Setting value
 */
function get_setting($key, $default = null) {
    global $conn;

    $sql = "SELECT setting_value FROM settings WHERE setting_key = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $key);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $row = $result->fetch_assoc()) {
        return $row['setting_value'];
    }

    return $default;
}

/**
 * تسجيل محاولة تسجيل الدخول
 *
 * @param string $username اسم المستخدم
 * @param bool $status حالة محاولة تسجيل الدخول (true للنجاح، false للفشل)
 * @return bool نجاح العملية
 */
function log_login_attempt($username, $status = false) {
    global $conn;

    // التحقق من وجود جدول محاولات تسجيل الدخول
    try {
        $check_table = $conn->query("SHOW TABLES LIKE 'login_attempts'");
        if ($check_table->num_rows === 0) {
            return false; // الجدول غير موجود
        }
    } catch (Exception $e) {
        error_log("Error checking login_attempts table: " . $e->getMessage());
        return false;
    }

    // الحصول على عنوان IP ومعلومات المتصفح
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $status_text = $status ? 'success' : 'failed';

    try {
        // تسجيل محاولة تسجيل الدخول
        $sql = "INSERT INTO login_attempts (username, ip_address, user_agent, status, attempt_time)
                VALUES (?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssss", $username, $ip_address, $user_agent, $status_text);
        return $stmt->execute();
    } catch (Exception $e) {
        error_log("Error logging login attempt: " . $e->getMessage());
        return false;
    }
}

/**
 * التحقق من قفل الحساب
 *
 * @param string $username اسم المستخدم
 * @return bool|array false إذا لم يكن الحساب مقفلاً، أو مصفوفة تحتوي على معلومات القفل
 */
function check_account_lock($username) {
    global $conn;

    // التحقق من وجود جدول قفل الحسابات
    try {
        $check_table = $conn->query("SHOW TABLES LIKE 'account_locks'");
        if ($check_table->num_rows === 0) {
            return false; // الجدول غير موجود
        }
    } catch (Exception $e) {
        error_log("Error checking account_locks table: " . $e->getMessage());
        return false;
    }

    try {
        // التحقق من وجود قفل للحساب
        $sql = "SELECT * FROM account_locks WHERE username = ? AND locked_until > NOW()";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            return $result->fetch_assoc(); // الحساب مقفل
        }

        // حذف القفل المنتهي إذا وجد
        $sql = "DELETE FROM account_locks WHERE username = ? AND locked_until <= NOW()";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $username);
        $stmt->execute();

        return false; // الحساب غير مقفل
    } catch (Exception $e) {
        error_log("Error checking account lock: " . $e->getMessage());
        return false;
    }
}

/**
 * قفل الحساب بعد عدة محاولات فاشلة
 *
 * @param string $username اسم المستخدم
 * @param int $max_attempts الحد الأقصى لعدد المحاولات الفاشلة (الافتراضي: 5)
 * @param int $lock_minutes مدة القفل بالدقائق (الافتراضي: 30)
 * @return bool نجاح العملية
 */
function lock_account_if_needed($username, $max_attempts = 5, $lock_minutes = 30) {
    global $conn;

    // التحقق من وجود الجداول المطلوبة
    try {
        $check_table = $conn->query("SHOW TABLES LIKE 'login_attempts'");
        if ($check_table->num_rows === 0) {
            return false; // جدول محاولات تسجيل الدخول غير موجود
        }

        $check_table = $conn->query("SHOW TABLES LIKE 'account_locks'");
        if ($check_table->num_rows === 0) {
            return false; // جدول قفل الحسابات غير موجود
        }
    } catch (Exception $e) {
        error_log("Error checking tables for account locking: " . $e->getMessage());
        return false;
    }

    try {
        // التحقق من عدد المحاولات الفاشلة في الساعة الأخيرة
        $sql = "SELECT COUNT(*) as failed_attempts FROM login_attempts
                WHERE username = ? AND status = 'failed' AND attempt_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();
        $failed_attempts = $result->fetch_assoc()['failed_attempts'];

        // إذا تجاوز عدد المحاولات الفاشلة الحد الأقصى، قم بقفل الحساب
        if ($failed_attempts >= $max_attempts) {
            $locked_until = date('Y-m-d H:i:s', time() + ($lock_minutes * 60));
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';

            // حذف أي قفل سابق
            $delete_sql = "DELETE FROM account_locks WHERE username = ?";
            $delete_stmt = $conn->prepare($delete_sql);
            $delete_stmt->bind_param("s", $username);
            $delete_stmt->execute();

            // إضافة قفل جديد
            $insert_sql = "INSERT INTO account_locks (username, ip_address, locked_until) VALUES (?, ?, ?)";
            $insert_stmt = $conn->prepare($insert_sql);
            $insert_stmt->bind_param("sss", $username, $ip_address, $locked_until);
            return $insert_stmt->execute();
        }

        return false; // لا حاجة للقفل
    } catch (Exception $e) {
        error_log("Error locking account: " . $e->getMessage());
        return false;
    }
}

/**
 * إضافة إشعار للمستخدم
 *
 * @param int $user_id معرف المستخدم
 * @param string $title عنوان الإشعار
 * @param string $message نص الإشعار
 * @param string $type نوع الإشعار (info, success, warning, danger)
 * @param string $link رابط اختياري للإشعار
 * @return bool نجاح العملية
 */
function add_notification($user_id, $title, $message, $type = 'info', $link = null) {
    global $conn;

    // التحقق من وجود جدول الإشعارات
    try {
        $check_table = $conn->query("SHOW TABLES LIKE 'notifications'");
        if ($check_table->num_rows === 0) {
            return false; // جدول الإشعارات غير موجود
        }
    } catch (Exception $e) {
        error_log("Error checking notifications table: " . $e->getMessage());
        return false;
    }

    // التحقق من وجود المستخدم
    $check_user_sql = "SELECT id FROM users WHERE id = ?";
    $check_user_stmt = $conn->prepare($check_user_sql);
    $check_user_stmt->bind_param("i", $user_id);
    $check_user_stmt->execute();
    $check_user_result = $check_user_stmt->get_result();

    if ($check_user_result->num_rows === 0) {
        return false; // المستخدم غير موجود
    }

    // تنظيف البيانات
    $title = sanitize($title);
    $message = sanitize($message);
    $type = in_array($type, ['info', 'success', 'warning', 'danger']) ? $type : 'info';
    $link = $link ? sanitize($link) : null;

    try {
        // إضافة الإشعار
        $sql = "INSERT INTO notifications (user_id, title, message, type, link, created_at)
                VALUES (?, ?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("issss", $user_id, $title, $message, $type, $link);
        return $stmt->execute();
    } catch (Exception $e) {
        error_log("Error adding notification: " . $e->getMessage());
        return false;
    }
}

/**
 * الحصول على إشعارات المستخدم
 *
 * @param int $user_id معرف المستخدم
 * @param bool $unread_only الحصول على الإشعارات غير المقروءة فقط
 * @param int $limit عدد الإشعارات المراد استرجاعها
 * @return array مصفوفة تحتوي على إشعارات المستخدم
 */
function get_notifications($user_id, $unread_only = false, $limit = 10) {
    global $conn;

    // التحقق من وجود جدول الإشعارات
    try {
        $check_table = $conn->query("SHOW TABLES LIKE 'notifications'");
        if ($check_table->num_rows === 0) {
            return []; // جدول الإشعارات غير موجود
        }
    } catch (Exception $e) {
        error_log("Error checking notifications table: " . $e->getMessage());
        return [];
    }

    try {
        // بناء استعلام الحصول على الإشعارات
        $sql = "SELECT * FROM notifications WHERE user_id = ?";

        if ($unread_only) {
            $sql .= " AND is_read = 0";
        }

        $sql .= " ORDER BY created_at DESC LIMIT ?";

        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $user_id, $limit);
        $stmt->execute();
        $result = $stmt->get_result();

        $notifications = [];
        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $notifications[] = $row;
            }
        }

        return $notifications;
    } catch (Exception $e) {
        error_log("Error getting notifications: " . $e->getMessage());
        return [];
    }
}

/**
 * تحديث حالة الإشعار إلى مقروء
 *
 * @param int $notification_id معرف الإشعار
 * @param int $user_id معرف المستخدم (للتحقق من ملكية الإشعار)
 * @return bool نجاح العملية
 */
function mark_notification_as_read($notification_id, $user_id) {
    global $conn;

    // التحقق من وجود جدول الإشعارات
    try {
        $check_table = $conn->query("SHOW TABLES LIKE 'notifications'");
        if ($check_table->num_rows === 0) {
            return false; // جدول الإشعارات غير موجود
        }
    } catch (Exception $e) {
        error_log("Error checking notifications table: " . $e->getMessage());
        return false;
    }

    try {
        // تحديث حالة الإشعار
        $sql = "UPDATE notifications SET is_read = 1, read_at = NOW()
                WHERE id = ? AND user_id = ? AND is_read = 0";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $notification_id, $user_id);
        $stmt->execute();

        return $stmt->affected_rows > 0;
    } catch (Exception $e) {
        error_log("Error marking notification as read: " . $e->getMessage());
        return false;
    }
}

/**
 * تحديث حالة جميع إشعارات المستخدم إلى مقروءة
 *
 * @param int $user_id معرف المستخدم
 * @return bool نجاح العملية
 */
function mark_all_notifications_as_read($user_id) {
    global $conn;

    // التحقق من وجود جدول الإشعارات
    try {
        $check_table = $conn->query("SHOW TABLES LIKE 'notifications'");
        if ($check_table->num_rows === 0) {
            return false; // جدول الإشعارات غير موجود
        }
    } catch (Exception $e) {
        error_log("Error checking notifications table: " . $e->getMessage());
        return false;
    }

    try {
        // تحديث حالة جميع الإشعارات
        $sql = "UPDATE notifications SET is_read = 1, read_at = NOW()
                WHERE user_id = ? AND is_read = 0";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $user_id);
        $stmt->execute();

        return true;
    } catch (Exception $e) {
        error_log("Error marking all notifications as read: " . $e->getMessage());
        return false;
    }
}

/**
 * الحصول على عدد الإشعارات غير المقروءة للمستخدم
 *
 * @param int $user_id معرف المستخدم
 * @return int عدد الإشعارات غير المقروءة
 */
function get_unread_notifications_count($user_id) {
    global $conn;

    // التحقق من وجود جدول الإشعارات
    try {
        $check_table = $conn->query("SHOW TABLES LIKE 'notifications'");
        if ($check_table->num_rows === 0) {
            return 0; // جدول الإشعارات غير موجود
        }
    } catch (Exception $e) {
        error_log("Error checking notifications table: " . $e->getMessage());
        return 0;
    }

    try {
        // الحصول على عدد الإشعارات غير المقروءة
        $sql = "SELECT COUNT(*) as count FROM notifications
                WHERE user_id = ? AND is_read = 0";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            return (int)$result->fetch_assoc()['count'];
        }

        return 0;
    } catch (Exception $e) {
        error_log("Error getting unread notifications count: " . $e->getMessage());
        return 0;
    }
}