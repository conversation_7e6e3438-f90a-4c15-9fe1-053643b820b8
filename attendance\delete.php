<?php
/**
 * Delete Attendance Record Page
 *
 * This file handles deleting an attendance record
 */

// Include header which includes all necessary files
require_once '../includes/header.php';

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    set_flash_message('معرّف سجل الحضور غير صالح', 'danger');
    redirect('/attendance/index.php');
}

// Get attendance record ID
$attendance_id = (int)$_GET['id'];

// Get attendance record details
$sql = "SELECT a.*, e.name as employee_name
        FROM attendance a
        JOIN employees e ON a.employee_id = e.id
        WHERE a.id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $attendance_id);
$stmt->execute();
$result = $stmt->get_result();

// Check if record exists
if ($result->num_rows === 0) {
    set_flash_message('سجل الحضور غير موجود', 'danger');
    redirect('/attendance/index.php');
}

// Get attendance data
$attendance = $result->fetch_assoc();

// Check if there is an approved leave for this employee on this date
function has_approved_leave($employee_id, $date) {
    global $conn;

    $sql = "SELECT COUNT(*) as count
            FROM leaves
            WHERE employee_id = ?
            AND status = 'approved'
            AND ? BETWEEN start_date AND end_date";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param("is", $employee_id, $date);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();

    return $row['count'] > 0;
}

// Check for approved leave
$has_approved_leave = has_approved_leave($attendance['employee_id'], $attendance['date']);

// If there is an approved leave, prevent deletion
if ($has_approved_leave) {
    set_flash_message('لا يمكن حذف سجل الحضور لأن الموظف لديه إجازة معتمدة في هذا التاريخ', 'danger');
    redirect('/attendance/index.php');
}

// Check if there are holiday rollovers linked to this attendance record
$rollover_sql = "SELECT COUNT(*) as count FROM holiday_rollovers WHERE attendance_id = ?";
$rollover_stmt = $conn->prepare($rollover_sql);
$rollover_stmt->bind_param("i", $attendance_id);
$rollover_stmt->execute();
$rollover_result = $rollover_stmt->get_result();
$rollover_row = $rollover_result->fetch_assoc();

if ($rollover_row['count'] > 0) {
    // Delete related holiday rollovers first
    $delete_rollovers_sql = "DELETE FROM holiday_rollovers WHERE attendance_id = ?";
    $delete_rollovers_stmt = $conn->prepare($delete_rollovers_sql);
    $delete_rollovers_stmt->bind_param("i", $attendance_id);
    $delete_rollovers_stmt->execute();
}

// Delete attendance record
$sql = "DELETE FROM attendance WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $attendance_id);

if ($stmt->execute()) {
    if ($stmt->affected_rows > 0) {
        // Log activity
        log_activity('delete_attendance', 'تم حذف سجل حضور الموظف: ' . $attendance['employee_name'] . ' ليوم ' . format_date($attendance['date']));

        // Set success message
        set_flash_message('تم حذف سجل الحضور بنجاح', 'success');
    } else {
        // No rows affected - record might not exist
        set_flash_message('السجل غير موجود أو تم حذفه مسبقاً', 'warning');
    }
} else {
    // Set error message
    set_flash_message('حدث خطأ أثناء حذف سجل الحضور: ' . $stmt->error, 'danger');
}

// Redirect to attendance list
redirect('/attendance/index.php');

// Include footer
require_once '../includes/footer.php';
?>
