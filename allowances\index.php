<?php
/**
 * Employee Allowances Management Page
 *
 * This file handles managing employee allowances
 */

// Include header
require_once '../includes/header.php';

// Check if user has permission
if (!is_admin() && !is_supervisor()) {
    set_flash_message('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger');
    redirect('../index.php');
}

// Initialize variables
$employee_id = isset($_GET['employee_id']) ? (int)$_GET['employee_id'] : 0;
$allowance_type_id = isset($_GET['allowance_type_id']) ? (int)$_GET['allowance_type_id'] : 0;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = RECORDS_PER_PAGE;
$offset = ($page - 1) * $limit;

// Get employee name if employee_id is provided
$employee_name = '';
if ($employee_id > 0) {
    $sql = "SELECT name FROM employees WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $employee_id);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result && $row = $result->fetch_assoc()) {
        $employee_name = $row['name'];
    }
}

// Build query
$sql = "SELECT ea.*, e.name as employee_name, e.employee_number, at.name as allowance_type, at.is_percentage
        FROM employee_allowances ea
        JOIN employees e ON ea.employee_id = e.id
        JOIN allowance_types at ON ea.allowance_type_id = at.id
        WHERE 1=1";
$count_sql = "SELECT COUNT(*) as total
              FROM employee_allowances ea
              JOIN employees e ON ea.employee_id = e.id
              JOIN allowance_types at ON ea.allowance_type_id = at.id
              WHERE 1=1";
$params = [];
$types = "";

// Add filters
if ($employee_id > 0) {
    $sql .= " AND ea.employee_id = ?";
    $count_sql .= " AND ea.employee_id = ?";
    $params[] = $employee_id;
    $types .= "i";
}

if ($allowance_type_id > 0) {
    $sql .= " AND ea.allowance_type_id = ?";
    $count_sql .= " AND ea.allowance_type_id = ?";
    $params[] = $allowance_type_id;
    $types .= "i";
}

// Add order by
$sql .= " ORDER BY e.name, at.name";

// Add limit and offset
$sql .= " LIMIT ?, ?";
$params[] = $offset;
$params[] = $limit;
$types .= "ii";

// تحقق من وجود جدول employee_allowances قبل الاستعلام
$table_exists = false;
$check_table_sql = "SHOW TABLES LIKE 'employee_allowances'";
$check_result = $conn->query($check_table_sql);
if ($check_result && $check_result->num_rows > 0) {
    $table_exists = true;
}

$allowances = [];
$total_records = 0;
$total_pages = 1;

if ($table_exists) {
    // Get allowances
    $stmt = $conn->prepare($sql);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $allowances[] = $row;
    }

    // Get total count
    $count_stmt = $conn->prepare($count_sql);
    if (!empty($params)) {
        // Remove the last two parameters (limit and offset)
        array_pop($params);
        array_pop($params);
        $types = substr($types, 0, -2);

        if (!empty($params)) {
            $count_stmt->bind_param($types, ...$params);
        }
    }
    $count_stmt->execute();
    $count_result = $count_stmt->get_result();
    $count_row = $count_result->fetch_assoc();
    $total_records = $count_row['total'];
    $total_pages = ceil($total_records / $limit);
} else {
    // عرض رسالة تنبيه إذا كان الجدول غير موجود
    set_flash_message('جدول مكافآت الموظفين غير موجود. يرجى إعادة ضبط قاعدة البيانات أو استعادة نسخة احتياطية.', 'warning');
}

// Get allowance types for filter
$allowance_types = [];
// تحقق من وجود جدول allowance_types قبل الاستعلام
$check_table_sql = "SHOW TABLES LIKE 'allowance_types'";
$check_result = $conn->query($check_table_sql);
if ($check_result && $check_result->num_rows > 0) {
    $allowance_types_sql = "SELECT id, name FROM allowance_types ORDER BY name";
    $allowance_types_result = $conn->query($allowance_types_sql);
    if ($allowance_types_result) {
        while ($row = $allowance_types_result->fetch_assoc()) {
            $allowance_types[] = $row;
        }
    }
} else {
    // عرض رسالة تنبيه إذا كان الجدول غير موجود
    set_flash_message('جدول أنواع المكافآت غير موجود. يرجى إعادة ضبط قاعدة البيانات أو استعادة نسخة احتياطية.', 'warning');
}

// Get employees for filter
$employees_sql = "SELECT id, name, employee_number FROM employees WHERE status = 'active' ORDER BY name";
$employees_result = $conn->query($employees_sql);
$employees = [];
if ($employees_result) {
    while ($row = $employees_result->fetch_assoc()) {
        $employees[] = $row;
    }
}

// Process delete request
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $allowance_id = (int)$_GET['delete'];

    // Delete allowance
    $sql = "DELETE FROM employee_allowances WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $allowance_id);

    if ($stmt->execute()) {
        // Log activity
        log_activity('delete_employee_allowance', 'تم حذف مكافأة موظف');

        // Set success message
        set_flash_message('تم حذف المكافأة بنجاح', 'success');
    } else {
        // Set error message
        set_flash_message('حدث خطأ أثناء حذف المكافأة', 'danger');
    }

    // Redirect to avoid resubmission
    redirect('index.php' . ($employee_id ? "?employee_id=$employee_id" : ''));
}
?>

<div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold">
        <?php echo $employee_id > 0 ? 'مكافآت الموظف: ' . $employee_name : 'إدارة مكافآت الموظفين'; ?>
    </h1>
    <div class="flex space-x-2 space-x-reverse">
        <a href="add.php<?php echo $employee_id ? "?employee_id=$employee_id" : ''; ?>" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-md flex items-center">
            <i class="fas fa-plus ml-2"></i> إضافة مكافأة جديدة
        </a>
        <a href="types.php" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md flex items-center">
            <i class="fas fa-cog ml-2"></i> أنواع المكافآت
        </a>
        <a href="../index.php" class="bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-md flex items-center">
            <i class="fas fa-arrow-right ml-2"></i> العودة إلى لوحة التحكم
        </a>
    </div>
</div>

<!-- Filter Form -->
<div class="bg-white rounded-lg shadow-md p-6 mb-6">
    <h3 class="text-lg font-semibold mb-4">تصفية النتائج</h3>
    <form method="GET" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
            <label for="employee_id" class="block text-sm font-medium text-gray-700 mb-1">الموظف</label>
            <select id="employee_id" name="employee_id" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                <option value="">الكل</option>
                <?php foreach ($employees as $employee): ?>
                    <option value="<?php echo $employee['id']; ?>" <?php echo $employee_id == $employee['id'] ? 'selected' : ''; ?>>
                        <?php echo $employee['name']; ?> (<?php echo $employee['employee_number']; ?>)
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

        <div>
            <label for="allowance_type_id" class="block text-sm font-medium text-gray-700 mb-1">نوع المكافأة</label>
            <select id="allowance_type_id" name="allowance_type_id" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
                <option value="">الكل</option>
                <?php foreach ($allowance_types as $type): ?>
                    <option value="<?php echo $type['id']; ?>" <?php echo $allowance_type_id == $type['id'] ? 'selected' : ''; ?>>
                        <?php echo $type['name']; ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="flex items-end">
            <button type="submit" class="bg-secondary hover:bg-secondary-dark text-white py-2 px-4 rounded-md">
                <i class="fas fa-search ml-2"></i> بحث
            </button>
            <a href="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" class="mr-2 bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-md">
                <i class="fas fa-redo ml-2"></i> إعادة تعيين
            </a>
        </div>
    </form>
</div>

<!-- Allowances List -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <div class="p-4 border-b">
        <h2 class="text-xl font-semibold">مكافآت الموظفين</h2>
    </div>

    <?php if (empty($allowances)): ?>
        <div class="p-6 text-center text-gray-500">
            لا توجد مكافآت مطابقة لمعايير البحث
        </div>
    <?php else: ?>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الموظف
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            نوع المكافأة
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            المبلغ
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            تاريخ البداية
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            تاريخ النهاية
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($allowances as $allowance): ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="../employees/view.php?id=<?php echo $allowance['employee_id']; ?>" class="text-primary hover:underline">
                                    <?php echo $allowance['employee_name']; ?> (<?php echo $allowance['employee_number']; ?>)
                                </a>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo $allowance['allowance_type']; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo $allowance['amount']; ?> <?php echo $allowance['is_percentage'] ? '%' : ''; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo format_date($allowance['effective_date']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo $allowance['end_date'] ? format_date($allowance['end_date']) : 'غير محدد'; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <a href="edit.php?id=<?php echo $allowance['id']; ?>" class="text-yellow-600 hover:text-yellow-900 ml-2">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="index.php?delete=<?php echo $allowance['id']; ?><?php echo $employee_id ? "&employee_id=$employee_id" : ''; ?>" class="text-red-600 hover:text-red-900 ml-2" onclick="return confirm('هل أنت متأكد من حذف هذه المكافأة؟');">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="px-6 py-4 bg-gray-50 border-t">
                <div class="flex justify-between items-center">
                    <div class="text-sm text-gray-500">
                        عرض <?php echo count($allowances); ?> من أصل <?php echo $total_records; ?> مكافأة
                    </div>
                    <div class="flex space-x-1 space-x-reverse">
                        <?php
                        // Build query string for pagination links
                        $query_params = array_filter([
                            'employee_id' => $employee_id ?: null,
                            'allowance_type_id' => $allowance_type_id ?: null
                        ]);
                        $query_string = http_build_query($query_params);
                        $query_string = !empty($query_string) ? '&' . $query_string : '';
                        ?>

                        <?php if ($page > 1): ?>
                            <a href="?page=<?php echo $page - 1 . $query_string; ?>" class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">
                                السابق
                            </a>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                            <a href="?page=<?php echo $i . $query_string; ?>" class="px-3 py-1 <?php echo $i === $page ? 'bg-primary text-white' : 'bg-white text-gray-700 hover:bg-gray-50'; ?> border border-gray-300 rounded-md text-sm">
                                <?php echo $i; ?>
                            </a>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                            <a href="?page=<?php echo $page + 1 . $query_string; ?>" class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">
                                التالي
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<?php
// Include footer
require_once '../includes/footer.php';
?>
