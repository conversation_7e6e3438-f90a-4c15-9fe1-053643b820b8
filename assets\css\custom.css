/**
 * Custom CSS for Employee Management System
 */

/* Table Styling */
table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
    border: 1px solid #e2e8f0;
}

table th,
table td {
    padding: 0.75rem;
    border: 1px solid #e2e8f0;
    text-align: right;
}

table th {
    background-color: #f8fafc;
    font-weight: 600;
    color: #1e293b;
}

table tr:nth-child(even) {
    background-color: #f9fafb;
}

table tr:hover {
    background-color: #f1f5f9;
}

/* Form Styling */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #1e293b;
}

.form-control {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    background-color: #fff;
}

.form-control:focus {
    outline: none;
    border-color: #FF6B00;
    box-shadow: 0 0 0 3px rgba(255, 107, 0, 0.1);
}

/* Button Styling */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: #FF6B00;
    color: #fff;
}

.btn-primary:hover {
    background-color: #E05A00;
}

.btn-secondary {
    background-color: #1F2937;
    color: #fff;
}

.btn-secondary:hover {
    background-color: #111827;
}

.btn-success {
    background-color: #10B981;
    color: #fff;
}

.btn-success:hover {
    background-color: #059669;
}

.btn-danger {
    background-color: #EF4444;
    color: #fff;
}

.btn-danger:hover {
    background-color: #DC2626;
}

/* Alert Styling */
.alert {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 0.375rem;
    border-right: 4px solid;
}

.alert-success {
    background-color: #ECFDF5;
    border-color: #10B981;
    color: #065F46;
}

.alert-danger {
    background-color: #FEF2F2;
    border-color: #EF4444;
    color: #991B1B;
}

.alert-warning {
    background-color: #FFFBEB;
    border-color: #F59E0B;
    color: #92400E;
}

.alert-info {
    background-color: #EFF6FF;
    border-color: #3B82F6;
    color: #1E40AF;
}

/* Card Styling */
.card {
    background-color: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    overflow: hidden;
}

.card-header {
    padding: 1rem;
    background-color: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    font-weight: 600;
}

.card-body {
    padding: 1rem;
}

.card-footer {
    padding: 1rem;
    background-color: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

/* Utility Classes */
.text-right {
    text-align: right;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.font-bold {
    font-weight: 700;
}

.text-primary {
    color: #FF6B00;
}

.text-secondary {
    color: #1F2937;
}

.text-success {
    color: #10B981;
}

.text-danger {
    color: #EF4444;
}

.text-warning {
    color: #F59E0B;
}

.text-info {
    color: #3B82F6;
}
