-- نسخة احتياطية لقاعدة البيانات payroll_system قبل إعادة الضبط
-- تاريخ الإنشاء: 2025-05-18 22:15:48
-- خادم: localhost
-- PHP نسخة: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET time_zone = "+00:00";

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

-- --------------------------------------------------------

DROP TABLE IF EXISTS `activity_log`;
CREATE TABLE `activity_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `timestamp` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `activity_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `allowance_types`;
CREATE TABLE `allowance_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `is_percentage` tinyint(1) DEFAULT 0,
  `is_taxable` tinyint(1) DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `attendance`;
CREATE TABLE `attendance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `date` date NOT NULL,
  `time_in` time DEFAULT NULL,
  `time_out` time DEFAULT NULL,
  `status` enum('present','absent','leave','late') NOT NULL DEFAULT 'present',
  `overtime_hours` decimal(5,2) DEFAULT 0.00,
  `notes` text DEFAULT NULL,
  `is_weekly_holiday` tinyint(1) NOT NULL DEFAULT 0,
  `is_rollover_eligible` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `employee_date` (`employee_id`,`date`),
  CONSTRAINT `attendance_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=84 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `attendance` VALUES ('1','7','2025-05-01','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:20');
INSERT INTO `attendance` VALUES ('2','20','2025-05-01','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:20');
INSERT INTO `attendance` VALUES ('3','18','2025-05-01','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:20');
INSERT INTO `attendance` VALUES ('4','9','2025-05-01','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:20');
INSERT INTO `attendance` VALUES ('5','10','2025-05-01','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:20');
INSERT INTO `attendance` VALUES ('6','14','2025-05-01','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:20');
INSERT INTO `attendance` VALUES ('7','2','2025-05-01','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:20');
INSERT INTO `attendance` VALUES ('8','17','2025-05-01','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:20');
INSERT INTO `attendance` VALUES ('9','6','2025-05-01','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:20');
INSERT INTO `attendance` VALUES ('10','16','2025-05-01','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:20');
INSERT INTO `attendance` VALUES ('11','12','2025-05-01','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:20');
INSERT INTO `attendance` VALUES ('12','13','2025-05-01','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:20');
INSERT INTO `attendance` VALUES ('13','19','2025-05-01','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:20');
INSERT INTO `attendance` VALUES ('14','15','2025-05-01','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:20');
INSERT INTO `attendance` VALUES ('15','1','2025-05-01','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:20');
INSERT INTO `attendance` VALUES ('16','4','2025-05-01','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:20');
INSERT INTO `attendance` VALUES ('17','5','2025-05-01','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:20');
INSERT INTO `attendance` VALUES ('18','3','2025-05-01','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:20');
INSERT INTO `attendance` VALUES ('19','8','2025-05-01','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:20');
INSERT INTO `attendance` VALUES ('20','11','2025-05-01','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:20');
INSERT INTO `attendance` VALUES ('21','7','2025-05-03','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:29');
INSERT INTO `attendance` VALUES ('22','20','2025-05-03','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:29');
INSERT INTO `attendance` VALUES ('23','18','2025-05-03','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:29');
INSERT INTO `attendance` VALUES ('24','9','2025-05-03','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:29');
INSERT INTO `attendance` VALUES ('25','10','2025-05-03','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:29');
INSERT INTO `attendance` VALUES ('26','14','2025-05-03','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:29');
INSERT INTO `attendance` VALUES ('27','2','2025-05-03','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:29');
INSERT INTO `attendance` VALUES ('28','17','2025-05-03','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:29');
INSERT INTO `attendance` VALUES ('29','6','2025-05-03','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:29');
INSERT INTO `attendance` VALUES ('30','16','2025-05-03','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:29');
INSERT INTO `attendance` VALUES ('31','12','2025-05-03','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:29');
INSERT INTO `attendance` VALUES ('32','13','2025-05-03','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:29');
INSERT INTO `attendance` VALUES ('33','19','2025-05-03','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:30');
INSERT INTO `attendance` VALUES ('34','15','2025-05-03','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:30');
INSERT INTO `attendance` VALUES ('35','1','2025-05-03','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:30');
INSERT INTO `attendance` VALUES ('36','4','2025-05-03','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:30');
INSERT INTO `attendance` VALUES ('37','5','2025-05-03','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:30');
INSERT INTO `attendance` VALUES ('38','3','2025-05-03','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:30');
INSERT INTO `attendance` VALUES ('39','8','2025-05-03','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:30');
INSERT INTO `attendance` VALUES ('40','11','2025-05-03','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:30');
INSERT INTO `attendance` VALUES ('41','7','2025-05-04','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:40');
INSERT INTO `attendance` VALUES ('42','20','2025-05-04','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:40');
INSERT INTO `attendance` VALUES ('43','18','2025-05-04','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:40');
INSERT INTO `attendance` VALUES ('44','9','2025-05-04','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:40');
INSERT INTO `attendance` VALUES ('45','10','2025-05-04','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:40');
INSERT INTO `attendance` VALUES ('46','14','2025-05-04','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:40');
INSERT INTO `attendance` VALUES ('47','2','2025-05-04','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:40');
INSERT INTO `attendance` VALUES ('48','17','2025-05-04','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:40');
INSERT INTO `attendance` VALUES ('49','6','2025-05-04','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:40');
INSERT INTO `attendance` VALUES ('50','16','2025-05-04','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:40');
INSERT INTO `attendance` VALUES ('51','12','2025-05-04','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:40');
INSERT INTO `attendance` VALUES ('52','13','2025-05-04','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:40');
INSERT INTO `attendance` VALUES ('53','19','2025-05-04','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:40');
INSERT INTO `attendance` VALUES ('54','15','2025-05-04','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:40');
INSERT INTO `attendance` VALUES ('55','1','2025-05-04','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:40');
INSERT INTO `attendance` VALUES ('56','4','2025-05-04','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:40');
INSERT INTO `attendance` VALUES ('57','5','2025-05-04','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:40');
INSERT INTO `attendance` VALUES ('58','3','2025-05-04','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:41');
INSERT INTO `attendance` VALUES ('59','8','2025-05-04','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:41');
INSERT INTO `attendance` VALUES ('60','11','2025-05-04','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:41');
INSERT INTO `attendance` VALUES ('61','7','2025-05-05','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:48');
INSERT INTO `attendance` VALUES ('62','20','2025-05-05','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:48');
INSERT INTO `attendance` VALUES ('63','18','2025-05-05','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:48');
INSERT INTO `attendance` VALUES ('64','9','2025-05-05','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:48');
INSERT INTO `attendance` VALUES ('65','10','2025-05-05','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:48');
INSERT INTO `attendance` VALUES ('66','14','2025-05-05','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:48');
INSERT INTO `attendance` VALUES ('67','2','2025-05-05','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:48');
INSERT INTO `attendance` VALUES ('68','17','2025-05-05','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:48');
INSERT INTO `attendance` VALUES ('69','6','2025-05-05','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:48');
INSERT INTO `attendance` VALUES ('70','16','2025-05-05','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:48');
INSERT INTO `attendance` VALUES ('71','12','2025-05-05','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:48');
INSERT INTO `attendance` VALUES ('72','13','2025-05-05','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:48');
INSERT INTO `attendance` VALUES ('73','19','2025-05-05','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:48');
INSERT INTO `attendance` VALUES ('74','15','2025-05-05','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:48');
INSERT INTO `attendance` VALUES ('75','1','2025-05-05','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:49');
INSERT INTO `attendance` VALUES ('76','4','2025-05-05','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:49');
INSERT INTO `attendance` VALUES ('77','5','2025-05-05','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:49');
INSERT INTO `attendance` VALUES ('78','3','2025-05-05','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:49');
INSERT INTO `attendance` VALUES ('79','8','2025-05-05','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:49');
INSERT INTO `attendance` VALUES ('80','11','2025-05-05','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:10:49');
INSERT INTO `attendance` VALUES ('81','1','2023-05-01','09:00:00','17:00:00','present','0.00',NULL,'0','0','2025-05-18 22:15:19');
INSERT INTO `attendance` VALUES ('82','2','2023-05-01','09:30:00','17:00:00','late','0.00','تأخر 30 دقيقة','0','0','2025-05-18 22:15:19');
INSERT INTO `attendance` VALUES ('83','3','2023-05-01',NULL,NULL,'absent','0.00','إجازة مرضية','0','0','2025-05-18 22:15:19');

DROP TABLE IF EXISTS `csrf_tokens`;
CREATE TABLE `csrf_tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `token` varchar(255) NOT NULL,
  `session_id` varchar(255) NOT NULL,
  `expires_at` datetime NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `token` (`token`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `csrf_tokens` VALUES ('1','b32ab9b84a2e1ba94e81c048d6d9aed33e2fa119fe0305011a37eaeb89ff4016','b454b19g5iutgiqkc5cmgsr8bl','2025-05-18 23:08:51','2025-05-18 22:08:51');
INSERT INTO `csrf_tokens` VALUES ('2','eb509b55b7e9c62decfd5f9c27e1050d0d5720909baa66682703577a808065e6','b454b19g5iutgiqkc5cmgsr8bl','2025-05-18 23:08:56','2025-05-18 22:08:56');
INSERT INTO `csrf_tokens` VALUES ('3','60bd366827112df77577afccc6b522f2a024d4dfbf3415dfd7b70c408efa3654','b454b19g5iutgiqkc5cmgsr8bl','2025-05-18 23:09:52','2025-05-18 22:09:52');
INSERT INTO `csrf_tokens` VALUES ('4','909fa034f6fa6826bfafde7513a3d5598a0c3a30ec72d875fdfe19b601d0f0f2','b454b19g5iutgiqkc5cmgsr8bl','2025-05-18 23:09:57','2025-05-18 22:09:57');
INSERT INTO `csrf_tokens` VALUES ('5','4caaeaaa3a0977755237885fd920c2d28b81fa9ebfa048af32df60b98b5d2090','b454b19g5iutgiqkc5cmgsr8bl','2025-05-18 23:10:04','2025-05-18 22:10:04');
INSERT INTO `csrf_tokens` VALUES ('6','7e2a09fdd170b1dc4a847c78ae9af56fa9792bd35fddf7ae8e5a2f705cfc67c0','b454b19g5iutgiqkc5cmgsr8bl','2025-05-18 23:10:20','2025-05-18 22:10:20');
INSERT INTO `csrf_tokens` VALUES ('7','2b15a06ec12182db0e6f22931ccf92c932bd30c72aa706a6d1534042dab178bd','b454b19g5iutgiqkc5cmgsr8bl','2025-05-18 23:10:20','2025-05-18 22:10:20');
INSERT INTO `csrf_tokens` VALUES ('8','bcdbba185cef9c1de473ebce661efc6d367ed914b81d17f92b4471c6a2a8cd1a','b454b19g5iutgiqkc5cmgsr8bl','2025-05-18 23:10:26','2025-05-18 22:10:26');
INSERT INTO `csrf_tokens` VALUES ('9','39481b4e65e61f1226a073f36b5dff75f9d0badc5c22597c394bf3859a5f3250','b454b19g5iutgiqkc5cmgsr8bl','2025-05-18 23:10:29','2025-05-18 22:10:29');
INSERT INTO `csrf_tokens` VALUES ('10','3a921c433282821ca06214655501a95f301a31d4167109b2b9dae36e510d9757','b454b19g5iutgiqkc5cmgsr8bl','2025-05-18 23:10:30','2025-05-18 22:10:30');
INSERT INTO `csrf_tokens` VALUES ('11','0811f072fb7ff7a1ef7a25feccbb274e3f7fb3b7d03e22b449c41c393e018255','b454b19g5iutgiqkc5cmgsr8bl','2025-05-18 23:10:37','2025-05-18 22:10:37');
INSERT INTO `csrf_tokens` VALUES ('12','51ab2918735f43706a86a75c46b73a5b0bb5516bbcc6e1ce04f26fd58443b993','b454b19g5iutgiqkc5cmgsr8bl','2025-05-18 23:10:40','2025-05-18 22:10:40');
INSERT INTO `csrf_tokens` VALUES ('13','2aafd608d72e3d44466e7dcd3ce7a20a1f7e204a816f46cabee27d6698ff9f57','b454b19g5iutgiqkc5cmgsr8bl','2025-05-18 23:10:41','2025-05-18 22:10:41');
INSERT INTO `csrf_tokens` VALUES ('14','fc65ad1fcc53e5a9685140dff2ba51f5faa384c20cb7332506f5e26032854aba','b454b19g5iutgiqkc5cmgsr8bl','2025-05-18 23:10:45','2025-05-18 22:10:45');
INSERT INTO `csrf_tokens` VALUES ('15','3e2332548654e832da634e3340ce3ad870249e02c892225084dc1cb3fc980154','b454b19g5iutgiqkc5cmgsr8bl','2025-05-18 23:10:48','2025-05-18 22:10:48');
INSERT INTO `csrf_tokens` VALUES ('16','2b8ceebe2f4b0e103b7a0d2e378a26ed5f4f17cbb31e25f9f92f0f42e55ae942','b454b19g5iutgiqkc5cmgsr8bl','2025-05-18 23:10:49','2025-05-18 22:10:49');
INSERT INTO `csrf_tokens` VALUES ('17','7cf810de6bfe9cc20b2406c56de5d0abfea2a78be1a24d8c806c40d473e40bd1','b454b19g5iutgiqkc5cmgsr8bl','2025-05-18 23:11:06','2025-05-18 22:11:06');
INSERT INTO `csrf_tokens` VALUES ('18','0187ba889bd26761cef6bbc0d4c68123f3f7360cbac7be71644948dc1e18f292','b454b19g5iutgiqkc5cmgsr8bl','2025-05-18 23:13:58','2025-05-18 22:13:58');
INSERT INTO `csrf_tokens` VALUES ('19','53f26b230c2f34a691294e37e3273ae6f59673b78ae2ab6046b0c13ff487737d','b454b19g5iutgiqkc5cmgsr8bl','2025-05-18 23:15:38','2025-05-18 22:15:38');
INSERT INTO `csrf_tokens` VALUES ('20','14aaf98536ff173bba7b48f059e660d86ff5b88dc745040765770f756b623573','b454b19g5iutgiqkc5cmgsr8bl','2025-05-18 23:15:44','2025-05-18 22:15:44');
INSERT INTO `csrf_tokens` VALUES ('21','2c7ef8f19fcfda4e78a98945bfef634ae4a546c624349c17a0340a6dd17e5cfd','b454b19g5iutgiqkc5cmgsr8bl','2025-05-18 23:15:48','2025-05-18 22:15:48');

DROP TABLE IF EXISTS `deduction_types`;
CREATE TABLE `deduction_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `is_percentage` tinyint(1) DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `departments`;
CREATE TABLE `departments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `employee_allowances`;
CREATE TABLE `employee_allowances` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `allowance_type_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `effective_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  KEY `allowance_type_id` (`allowance_type_id`),
  CONSTRAINT `employee_allowances_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
  CONSTRAINT `employee_allowances_ibfk_2` FOREIGN KEY (`allowance_type_id`) REFERENCES `allowance_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `employee_deductions`;
CREATE TABLE `employee_deductions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `deduction_type_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `effective_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  KEY `deduction_type_id` (`deduction_type_id`),
  CONSTRAINT `employee_deductions_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
  CONSTRAINT `employee_deductions_ibfk_2` FOREIGN KEY (`deduction_type_id`) REFERENCES `deduction_types` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `employees`;
CREATE TABLE `employees` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_number` varchar(20) NOT NULL,
  `name` varchar(100) NOT NULL,
  `position` varchar(100) NOT NULL,
  `department` varchar(100) NOT NULL,
  `join_date` date NOT NULL,
  `basic_salary` decimal(10,2) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `notes` text DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `employee_number` (`employee_number`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `employees` VALUES ('1','EMP001','علي محمد','مدير الحسابات','الادارة العامة','0000-00-00','8000.00','01002206642','<EMAIL>','active','ملاحظات عن الموظف',NULL,'2025-05-18 22:09:21',NULL);
INSERT INTO `employees` VALUES ('2','EMP002','الحاج احمد عكاشة','رئيس مجلس الادارة','الإدارة العامة','0000-00-00','8000.00','01002206643','<EMAIL>','active',NULL,NULL,'2025-05-18 22:09:21',NULL);
INSERT INTO `employees` VALUES ('3','EMP003','محمود عكاشة','مدير الشركة','الإدارة العامة','0000-00-00','8000.00','01002206644','<EMAIL>','active',NULL,NULL,'2025-05-18 22:09:21',NULL);
INSERT INTO `employees` VALUES ('4','EMP004','عمرو محمد عبيد','مدير مصنع','الإدارة العامة','0000-00-00','8000.00','01002206645','<EMAIL>','active',NULL,NULL,'2025-05-18 22:09:21',NULL);
INSERT INTO `employees` VALUES ('5','EMP005','محمد زكريا','مدير المبيعات','الإدارة العامة','0000-00-00','8000.00','01002206646','<EMAIL>','active',NULL,NULL,'2025-05-18 22:09:21',NULL);
INSERT INTO `employees` VALUES ('6','EMP006','ثروت','فني تشغيل','الانتاج','0000-00-00','5000.00','01002206647','<EMAIL>','active',NULL,NULL,'2025-05-18 22:09:21',NULL);
INSERT INTO `employees` VALUES ('7','EMP007','ابوالحسن','عامل انتاج','الانتاج','0000-00-00','5000.00','01002206648','<EMAIL>','active',NULL,NULL,'2025-05-18 22:09:21',NULL);
INSERT INTO `employees` VALUES ('8','EMP008','محمود نجيب','فني تشغيل','الانتاج','0000-00-00','6000.00','01002206649','<EMAIL>','active',NULL,NULL,'2025-05-18 22:09:21',NULL);
INSERT INTO `employees` VALUES ('9','EMP009','احمد نجيب','سائق','الانتاج','0000-00-00','5000.00','01002206650','<EMAIL>','active',NULL,NULL,'2025-05-18 22:09:21',NULL);
INSERT INTO `employees` VALUES ('10','EMP010','اشرف','مندوب','الانتاج','0000-00-00','5000.00','01002206651','<EMAIL>','active',NULL,NULL,'2025-05-18 22:09:21',NULL);
INSERT INTO `employees` VALUES ('11','EMP011','ملك','عاملة انتاج','الانتاج','0000-00-00','4000.00','01002206652','<EMAIL>','active',NULL,NULL,'2025-05-18 22:09:21',NULL);
INSERT INTO `employees` VALUES ('12','EMP012','دنيا','عاملة انتاج','الانتاج','0000-00-00','4000.00','01002206653','<EMAIL>','active',NULL,NULL,'2025-05-18 22:09:21',NULL);
INSERT INTO `employees` VALUES ('13','EMP013','راندا','مندوب','الانتاج','0000-00-00','5000.00','01002206654','<EMAIL>','active',NULL,NULL,'2025-05-18 22:09:21',NULL);
INSERT INTO `employees` VALUES ('14','EMP014','الاء','مندوب','الانتاج','0000-00-00','5000.00','01002206655','<EMAIL>','active',NULL,NULL,'2025-05-18 22:09:21',NULL);
INSERT INTO `employees` VALUES ('15','EMP015','شهد','عاملة انتاج','الانتاج','0000-00-00','4000.00','01002206656','<EMAIL>','active',NULL,NULL,'2025-05-18 22:09:21',NULL);
INSERT INTO `employees` VALUES ('16','EMP016','جومانه','عاملة انتاج','الانتاج','0000-00-00','4000.00','01002206657','<EMAIL>','active',NULL,NULL,'2025-05-18 22:09:21',NULL);
INSERT INTO `employees` VALUES ('17','EMP017','ام ملك','عاملة انتاج','الانتاج','0000-00-00','3000.00','01002206658','<EMAIL>','active',NULL,NULL,'2025-05-18 22:09:21',NULL);
INSERT INTO `employees` VALUES ('18','EMP018','احمد جاد','عامل انتاج','الانتاج','0000-00-00','5000.00','01002206659','<EMAIL>','active',NULL,NULL,'2025-05-18 22:09:21',NULL);
INSERT INTO `employees` VALUES ('19','EMP019','سلمي','عاملة انتاج','الانتاج','0000-00-00','4000.00','01002206660','<EMAIL>','active',NULL,NULL,'2025-05-18 22:09:21',NULL);
INSERT INTO `employees` VALUES ('20','EMP020','ابوشنب','سائق','الانتاج','0000-00-00','12000.00','01002206661','<EMAIL>','active',NULL,NULL,'2025-05-18 22:09:21',NULL);

DROP TABLE IF EXISTS `holiday_rollovers`;
CREATE TABLE `holiday_rollovers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `attendance_id` int(11) NOT NULL COMMENT 'رقم سجل الحضور في يوم الإجازة',
  `rollover_date` date NOT NULL COMMENT 'تاريخ الترحيل (آخر يوم في الشهر)',
  `expiry_date` date NOT NULL COMMENT 'تاريخ انتهاء صلاحية الإجازة المرحلة',
  `is_used` tinyint(1) DEFAULT 0 COMMENT 'هل تم استخدام الإجازة المرحلة',
  `used_in_leave_id` int(11) DEFAULT NULL COMMENT 'رقم طلب الإجازة الذي تم استخدام الإجازة المرحلة فيه',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  KEY `attendance_id` (`attendance_id`),
  CONSTRAINT `holiday_rollovers_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
  CONSTRAINT `holiday_rollovers_ibfk_2` FOREIGN KEY (`attendance_id`) REFERENCES `attendance` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `leave_attachments`;
CREATE TABLE `leave_attachments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `leave_id` int(11) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(255) NOT NULL,
  `file_type` varchar(100) DEFAULT NULL,
  `file_size` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `leave_id` (`leave_id`),
  CONSTRAINT `leave_attachments_ibfk_1` FOREIGN KEY (`leave_id`) REFERENCES `leaves` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `leave_types`;
CREATE TABLE `leave_types` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `color` varchar(20) DEFAULT '#3490dc',
  `max_days_per_year` int(11) DEFAULT 0,
  `is_paid` tinyint(1) DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `leave_types` VALUES ('1','إجازة سنوية','#3B82F6','30','1','2025-05-18 22:08:50',NULL);
INSERT INTO `leave_types` VALUES ('2','إجازة مرضية','#EF4444','15','1','2025-05-18 22:08:50',NULL);
INSERT INTO `leave_types` VALUES ('3','إجازة بدون راتب','#9CA3AF','0','0','2025-05-18 22:08:50',NULL);
INSERT INTO `leave_types` VALUES ('4','إجازة طارئة','#F59E0B','5','1','2025-05-18 22:08:50',NULL);
INSERT INTO `leave_types` VALUES ('5','إجازة أمومة','#EC4899','70','1','2025-05-18 22:08:50',NULL);
INSERT INTO `leave_types` VALUES ('6','إجازة أبوة','#8B5CF6','3','1','2025-05-18 22:08:50',NULL);

DROP TABLE IF EXISTS `leaves`;
CREATE TABLE `leaves` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `leave_type_id` int(11) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `total_days` decimal(5,1) NOT NULL,
  `status` enum('pending','approved','rejected','cancelled') DEFAULT 'pending',
  `reason` text DEFAULT NULL,
  `use_rollover` tinyint(1) NOT NULL DEFAULT 0,
  `rollover_days_used` int(11) NOT NULL DEFAULT 0,
  `rejection_reason` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `rejected_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  KEY `leave_type_id` (`leave_type_id`),
  KEY `created_by` (`created_by`),
  KEY `approved_by` (`approved_by`),
  KEY `rejected_by` (`rejected_by`),
  CONSTRAINT `leaves_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
  CONSTRAINT `leaves_ibfk_2` FOREIGN KEY (`leave_type_id`) REFERENCES `leave_types` (`id`) ON DELETE CASCADE,
  CONSTRAINT `leaves_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `leaves_ibfk_4` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `leaves_ibfk_5` FOREIGN KEY (`rejected_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `positions`;
CREATE TABLE `positions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `salaries`;
CREATE TABLE `salaries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL,
  `month` int(2) NOT NULL,
  `year` int(4) NOT NULL,
  `basic_salary` decimal(10,2) NOT NULL,
  `allowances` decimal(10,2) DEFAULT 0.00,
  `deductions` decimal(10,2) DEFAULT 0.00,
  `overtime_pay` decimal(10,2) DEFAULT 0.00,
  `total_salary` decimal(10,2) NOT NULL,
  `payment_date` date DEFAULT NULL,
  `status` enum('paid','pending') NOT NULL DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `employee_month_year` (`employee_id`,`month`,`year`),
  CONSTRAINT `salaries_ibfk_1` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `salary_payments`;
CREATE TABLE `salary_payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `salary_id` int(11) NOT NULL,
  `payment_date` date NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_method` varchar(50) NOT NULL,
  `reference_number` varchar(100) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `salary_id` (`salary_id`),
  CONSTRAINT `salary_payments_ibfk_1` FOREIGN KEY (`salary_id`) REFERENCES `salaries` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `settings`;
CREATE TABLE `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(50) NOT NULL,
  `setting_value` text NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `settings` VALUES ('1','company_name','شركتي','اسم الشركة',NULL);
INSERT INTO `settings` VALUES ('2','company_address','الرياض، المملكة العربية السعودية','عنوان الشركة',NULL);
INSERT INTO `settings` VALUES ('3','company_phone','+966 12 345 6789','رقم هاتف الشركة',NULL);
INSERT INTO `settings` VALUES ('4','company_email','<EMAIL>','البريد الإلكتروني للشركة',NULL);
INSERT INTO `settings` VALUES ('5','company_logo','logo.png','شعار الشركة',NULL);
INSERT INTO `settings` VALUES ('6','system_language','ar','لغة النظام',NULL);
INSERT INTO `settings` VALUES ('7','currency_symbol','ج.م','رمز العملة',NULL);
INSERT INTO `settings` VALUES ('8','date_format','Y-m-d','صيغة التاريخ',NULL);
INSERT INTO `settings` VALUES ('9','time_format','h:i A','صيغة الوقت',NULL);
INSERT INTO `settings` VALUES ('10','overtime_rate','1.5','معدل العمل الإضافي',NULL);
INSERT INTO `settings` VALUES ('11','work_hours_per_day','8','ساعات العمل اليومية',NULL);
INSERT INTO `settings` VALUES ('12','work_days_per_month','22','أيام العمل الشهرية',NULL);
INSERT INTO `settings` VALUES ('13','weekly_holiday_day','5','يوم الإجازة الأسبوعية (5 = الجمعة)',NULL);
INSERT INTO `settings` VALUES ('14','enable_holiday_rollover','1','تفعيل ترحيل الإجازات الأسبوعية',NULL);
INSERT INTO `settings` VALUES ('15','max_rollover_days','10','الحد الأقصى لعدد أيام الإجازات المرحلة',NULL);
INSERT INTO `settings` VALUES ('16','rollover_expiry_months','3','عدد أشهر صلاحية الإجازات المرحلة',NULL);
INSERT INTO `settings` VALUES ('17','default_time_in','09:00','وقت الحضور الافتراضي',NULL);
INSERT INTO `settings` VALUES ('18','default_time_out','17:00','وقت الانصراف الافتراضي',NULL);

DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `name` varchar(100) NOT NULL,
  `role` enum('admin','supervisor','user') NOT NULL DEFAULT 'user',
  `last_login` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `users` VALUES ('1','admin','$2y$12$Ht1EiKHFTr5Hy.Oa9Yx.eeJjH3hRZ9E/oyK2jlXNr7yQfDmYkE3Oe','مدير النظام','admin',NULL,'2025-05-18 22:08:50',NULL);

DROP TABLE IF EXISTS `weekly_holidays`;
CREATE TABLE `weekly_holidays` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `day_of_week` int(11) NOT NULL COMMENT '5 للجمعة (0 = الأحد، 1 = الإثنين، ... 6 = السبت)',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `weekly_holidays` VALUES ('1','5','1','2025-05-18 22:08:50',NULL);

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
