<?php
/**
 * Allowance Types Management Page
 *
 * This file handles managing allowance types
 */

// Include header
require_once '../includes/header.php';

// Check if user has permission
if (!is_admin() && !is_supervisor()) {
    set_flash_message('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'danger');
    redirect('../index.php');
}

// Check and update table structure if needed
$check_columns = $conn->query("SHOW COLUMNS FROM allowance_types LIKE 'default_value'");
if ($check_columns->num_rows === 0) {
    // Add missing columns
    $alter_queries = [
        "ALTER TABLE allowance_types ADD COLUMN description text DEFAULT NULL AFTER name",
        "ALTER TABLE allowance_types ADD COLUMN default_value decimal(10,2) DEFAULT 0.00 AFTER is_percentage"
    ];

    foreach ($alter_queries as $query) {
        try {
            $conn->query($query);
        } catch (Exception $e) {
            // Column might already exist, continue
        }
    }
}

// Initialize variables
$allowance_type = [
    'id' => 0,
    'name' => '',
    'description' => '',
    'is_percentage' => 0,
    'default_value' => 0
];
$errors = [];
$edit_mode = false;

// Process edit request
if (isset($_GET['edit']) && !empty($_GET['edit'])) {
    $allowance_type_id = (int)$_GET['edit'];

    // Get allowance type details
    $sql = "SELECT * FROM allowance_types WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $allowance_type_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $allowance_type = [
            'id' => $row['id'],
            'name' => $row['name'],
            'description' => isset($row['description']) ? $row['description'] : '',
            'is_percentage' => $row['is_percentage'],
            'default_value' => isset($row['default_value']) ? $row['default_value'] : 0
        ];
        $edit_mode = true;
    }
}

// Process delete request
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $allowance_type_id = (int)$_GET['delete'];

    // Check if allowance type is used in any employee allowance
    $sql = "SELECT COUNT(*) as count FROM employee_allowances WHERE allowance_type_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $allowance_type_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();

    if ($row['count'] > 0) {
        set_flash_message('لا يمكن حذف نوع المكافأة لأنه مستخدم في مكافآت الموظفين', 'danger');
    } else {
        // Delete allowance type
        $sql = "DELETE FROM allowance_types WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $allowance_type_id);

        if ($stmt->execute()) {
            // Log activity
            log_activity('delete_allowance_type', 'تم حذف نوع مكافأة');

            // Set success message
            set_flash_message('تم حذف نوع المكافأة بنجاح', 'success');
        } else {
            // Set error message
            set_flash_message('حدث خطأ أثناء حذف نوع المكافأة', 'danger');
        }
    }

    // Redirect to avoid resubmission
    redirect('types.php');
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
        $errors['csrf'] = 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.';
    } else {
        // Get and sanitize input
        $allowance_type['id'] = isset($_POST['id']) ? (int)$_POST['id'] : 0;
        $allowance_type['name'] = isset($_POST['name']) ? sanitize($_POST['name']) : '';
        $allowance_type['description'] = isset($_POST['description']) ? sanitize($_POST['description']) : '';
        $allowance_type['is_percentage'] = isset($_POST['is_percentage']) ? 1 : 0;
        $allowance_type['default_value'] = isset($_POST['default_value']) ? (float)$_POST['default_value'] : 0;

        // Validate input
        if (empty($allowance_type['name'])) {
            $errors['name'] = 'يرجى إدخال اسم نوع المكافأة.';
        }

        // If no errors, save allowance type
        if (empty($errors)) {
            if ($allowance_type['id'] > 0) {
                // Update existing allowance type
                $sql = "UPDATE allowance_types
                        SET name = ?,
                            description = ?,
                            is_percentage = ?,
                            default_value = ?,
                            updated_at = NOW()
                        WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("ssidi",
                    $allowance_type['name'],
                    $allowance_type['description'],
                    $allowance_type['is_percentage'],
                    $allowance_type['default_value'],
                    $allowance_type['id']
                );

                if ($stmt->execute()) {
                    // Log activity
                    log_activity('update_allowance_type', 'تم تحديث نوع مكافأة: ' . $allowance_type['name']);

                    // Set success message
                    set_flash_message('تم تحديث نوع المكافأة بنجاح', 'success');

                    // Reset form
                    $allowance_type = [
                        'id' => 0,
                        'name' => '',
                        'description' => '',
                        'is_percentage' => 0,
                        'default_value' => 0
                    ];
                    $edit_mode = false;
                } else {
                    $errors['db'] = 'حدث خطأ أثناء تحديث نوع المكافأة. يرجى المحاولة مرة أخرى.';
                }
            } else {
                // Insert new allowance type
                $sql = "INSERT INTO allowance_types (name, description, is_percentage, default_value, created_at)
                        VALUES (?, ?, ?, ?, NOW())";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("ssid",
                    $allowance_type['name'],
                    $allowance_type['description'],
                    $allowance_type['is_percentage'],
                    $allowance_type['default_value']
                );

                if ($stmt->execute()) {
                    // Log activity
                    log_activity('add_allowance_type', 'تم إضافة نوع مكافأة جديد: ' . $allowance_type['name']);

                    // Set success message
                    set_flash_message('تم إضافة نوع المكافأة بنجاح', 'success');

                    // Reset form
                    $allowance_type = [
                        'id' => 0,
                        'name' => '',
                        'description' => '',
                        'is_percentage' => 0,
                        'default_value' => 0
                    ];
                } else {
                    $errors['db'] = 'حدث خطأ أثناء إضافة نوع المكافأة. يرجى المحاولة مرة أخرى.';
                }
            }

            // Redirect to avoid resubmission
            redirect('types.php');
        }
    }
}

// Get all allowance types
$sql = "SELECT * FROM allowance_types ORDER BY name";
$result = $conn->query($sql);
$allowance_types = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $allowance_types[] = $row;
    }
}

// Generate CSRF token
$csrf_token = generate_csrf_token();
?>

<div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold">إدارة أنواع المكافآت</h1>
    <div class="flex space-x-2 space-x-reverse">
        <a href="index.php" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-md flex items-center">
            <i class="fas fa-list ml-2"></i> قائمة المكافآت
        </a>
        <a href="../index.php" class="bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-md flex items-center">
            <i class="fas fa-arrow-right ml-2"></i> العودة إلى لوحة التحكم
        </a>
    </div>
</div>

<?php if (!empty($errors)): ?>
    <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 mb-6 rounded-md">
        <ul class="list-disc list-inside">
            <?php foreach ($errors as $error): ?>
                <li><?php echo $error; ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- Allowance Types Form -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4"><?php echo $edit_mode ? 'تعديل نوع المكافأة' : 'إضافة نوع مكافأة جديد'; ?></h2>
        <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
            <input type="hidden" name="id" value="<?php echo $allowance_type['id']; ?>">

            <div class="mb-4">
                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">الاسم <span class="text-red-600">*</span></label>
                <input type="text" id="name" name="name" value="<?php echo $allowance_type['name']; ?>" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary" required>
            </div>

            <div class="mb-4">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-1">الوصف</label>
                <textarea id="description" name="description" rows="3" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary"><?php echo $allowance_type['description']; ?></textarea>
            </div>

            <div class="mb-4">
                <label for="default_value" class="block text-sm font-medium text-gray-700 mb-1">القيمة الافتراضية</label>
                <input type="number" id="default_value" name="default_value" value="<?php echo $allowance_type['default_value']; ?>" step="0.01" min="0" class="w-full border border-gray-300 rounded-md py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary">
            </div>

            <div class="mb-4">
                <label class="flex items-center">
                    <input type="checkbox" name="is_percentage" <?php echo $allowance_type['is_percentage'] ? 'checked' : ''; ?> class="rounded text-primary focus:ring-primary">
                    <span class="mr-2">نسبة مئوية من الراتب الأساسي</span>
                </label>
            </div>

            <!-- تم إزالة خيار "خاضع للضريبة" -->

            <div class="flex justify-end">
                <?php if ($edit_mode): ?>
                    <a href="types.php" class="bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-md ml-2">
                        إلغاء
                    </a>
                <?php endif; ?>
                <button type="submit" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-md">
                    <i class="fas fa-save ml-2"></i> <?php echo $edit_mode ? 'تحديث' : 'إضافة'; ?>
                </button>
            </div>
        </form>
    </div>

    <!-- Allowance Types List -->
    <div class="bg-white rounded-lg shadow-md p-6 md:col-span-2">
        <h2 class="text-xl font-semibold mb-4">أنواع المكافآت</h2>

        <?php if (empty($allowance_types)): ?>
            <p class="text-gray-500 text-center py-4">لا توجد أنواع مكافآت</p>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                الاسم
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                القيمة الافتراضية
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                نسبة مئوية
                            </th>
                            <!-- تم إزالة عمود "خاضع للضريبة" -->
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                الإجراءات
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($allowance_types as $type): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="font-medium"><?php echo $type['name']; ?></span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo isset($type['default_value']) ? $type['default_value'] : '0'; ?> <?php echo $type['is_percentage'] ? '%' : ''; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo $type['is_percentage'] ? '<span class="text-green-600"><i class="fas fa-check"></i></span>' : '<span class="text-red-600"><i class="fas fa-times"></i></span>'; ?>
                                </td>
                                <!-- تم إزالة عرض "خاضع للضريبة" -->
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <a href="types.php?edit=<?php echo $type['id']; ?>" class="text-yellow-600 hover:text-yellow-900 ml-2">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="types.php?delete=<?php echo $type['id']; ?>" class="text-red-600 hover:text-red-900 ml-2" onclick="return confirm('هل أنت متأكد من حذف هذا النوع؟');">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
// Include footer
require_once '../includes/footer.php';
?>
